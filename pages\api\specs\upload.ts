/**
 * API Route: Upload and process OpenAPI specification
 * Replaces Papermark's document upload functionality
 */
import { NextApiRequest, NextApiResponse } from "next";

import formidable from "formidable";
import fs from "fs";
import { getServerSession } from "next-auth/next";
import { z } from "zod";

import {
  APIError,
  createSuccessResponse,
  requireAuth,
  validateFileUpload,
  validateMethod,
  withErrorHandling,
} from "@/lib/api-utils";
import prisma from "@/lib/prisma";
import { createRestGPTEngine } from "@/lib/restgpt-engine";
import { CustomUser } from "@/lib/types";

import { authOptions } from "../auth/[...nextauth]";

export const config = {
  api: {
    bodyParser: false,
  },
};

interface ApiSpecUploadRequest {
  teamId?: string;
  name: string;
  description?: string;
  baseUrl: string;
}

// Validation schemas
const uploadSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z.string().optional(),
  baseUrl: z.string().url("Invalid base URL").optional(),
  teamId: z.string().optional(),
});

async function uploadHandler(req: NextApiRequest, res: NextApiResponse) {
  validateMethod(req, ["POST"]);
  const session = await requireAuth(req, res);

  // Parse form data
  const form = formidable({
    maxFileSize: 10 * 1024 * 1024, // 10MB limit
  });

  const [fields, files] = await form.parse(req);

  const name = Array.isArray(fields.name) ? fields.name[0] : fields.name;
  const description = Array.isArray(fields.description)
    ? fields.description[0]
    : fields.description;
  const baseUrl = Array.isArray(fields.baseUrl)
    ? fields.baseUrl[0]
    : fields.baseUrl;
  const teamId = Array.isArray(fields.teamId)
    ? fields.teamId[0]
    : fields.teamId;

  // Validate form fields
  const validatedData = uploadSchema.parse({
    name,
    description,
    baseUrl,
    teamId,
  });

  // Get uploaded file
  const uploadedFile = Array.isArray(files.file) ? files.file[0] : files.file;
  if (!uploadedFile) {
    return res
      .status(400)
      .json({ error: "No OpenAPI specification file uploaded" });
  }

  // Read and parse the OpenAPI spec
  const fileContent = fs.readFileSync(uploadedFile.filepath, "utf8");
  let openApiSpec;

  try {
    openApiSpec = JSON.parse(fileContent);
  } catch (error) {
    try {
      // Try YAML parsing if JSON fails
      const yaml = require("yaml");
      openApiSpec = yaml.parse(fileContent);
    } catch (yamlError) {
      return res
        .status(400)
        .json({ error: "Invalid OpenAPI specification format" });
    }
  }

  // Initialize RestGPT engine to validate and process the spec
  const engine = createRestGPTEngine({
    enableLogging: true,
  });

  const initResult = await engine.initialize(
    openApiSpec,
    validatedData.baseUrl!,
  );

  if (!initResult.success) {
    return res.status(400).json({
      error: "Invalid OpenAPI specification",
      details: initResult.errors,
    });
  }

  // Save to database
  const apiSpec = await prisma.apiSpecification.create({
    data: {
      name: validatedData.name,
      description: validatedData.description,
      baseUrl: validatedData.baseUrl!,
      openApiSpec: openApiSpec,
      version: openApiSpec.openapi || openApiSpec.swagger,
      originalFile: uploadedFile.originalFilename || "uploaded-spec",
      fileSize: uploadedFile.size,
      status: "ACTIVE",
      enabled: true,
      toolCount: initResult.tools.length,
      ownerId: (session.user as CustomUser).id,
      teamId: validatedData.teamId || (session.user as CustomUser).id, // Use teamId if provided, otherwise use userId as fallback
    },
  });

  // Save generated tools to database
  const toolsData = initResult.tools.map((tool: any) => ({
    name: tool.name,
    description: tool.description,
    enabled: true,
    userModified: false,
    endpoint: tool.endpoint,
    parameters: tool.parameters,
    responseSchema: tool.responseSchema,
    apiSpecId: apiSpec.id,
  }));

  await prisma.functionTool.createMany({
    data: toolsData,
  });

  res.status(200).json(
    createSuccessResponse(
      {
        id: apiSpec.id,
        name: apiSpec.name,
        description: apiSpec.description,
        baseUrl: apiSpec.baseUrl,
        toolCount: apiSpec.toolCount,
        tools: initResult.tools,
        createdAt: apiSpec.createdAt.toISOString(),
      },
      "API specification uploaded and processed successfully",
    ),
  );
}

export default withErrorHandling(uploadHandler);
