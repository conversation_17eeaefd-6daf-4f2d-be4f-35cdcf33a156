/**
 * API Route: Chat with AI Agent
 * This implements the conversational interface for API agents
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import { CustomUser } from "@/lib/types";

import prisma from "../../../../lib/prisma";
import {
  APICallPlan,
  ConversationContext,
  ExecutionResult,
  FunctionTool,
  OpenAPISpec,
  createRestGPTEngine,
} from "../../../../lib/restgpt-engine";
import { authOptions } from "../../auth/[...nextauth]";

interface ChatRequest {
  message: string;
  sessionId?: string;
  context?: Record<string, any>;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { agentId } = req.query;
  if (!agentId || typeof agentId !== "string") {
    return res.status(400).json({ error: "Invalid agent ID" });
  }

  // Handle initial data fetch for the chat UI
  if (req.method === "GET") {
    // Get agent from database
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        OR: [
          { ownerId: (session.user as any).id },
          { team: { users: { some: { userId: (session.user as any).id } } } },
        ],
      },
      include: {
        apiSpec: {
          select: {
            id: true,
            name: true,
          },
        },
        tools: {
          select: {
            id: true,
          },
        },
      },
    });

    if (!agent) {
      return res.status(404).json({
        error: "Agent not found",
      });
    }

    const formattedAgent = {
      id: agent.id,
      name: agent.name,
      description: agent.description,
      specId: agent.apiSpecId,
      specName: agent.apiSpec.name,
      status: agent.status.toLowerCase(),
      toolCount: agent.tools.length,
      conversationCount: agent.conversationCount,
      configuration: agent.modelConfig,
    };

    const emptyMessages: any[] = [];

    const conversationStub = {
      id: `conv_${Date.now()}`,
      title: "New Conversation",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return res.status(200).json({
      success: true,
      data: {
        agent: formattedAgent,
        messages: emptyMessages,
        conversation: conversationStub,
      },
    });
  }

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { message, sessionId, context }: ChatRequest = req.body;

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Get agent configuration from database
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        OR: [
          { ownerId: (session.user as CustomUser).id },
          {
            team: {
              users: { some: { userId: (session.user as CustomUser).id } },
            },
          },
        ],
      },
      include: {
        apiSpec: true,
        tools: {
          include: {
            tool: true,
          },
          where: {
            enabled: true,
          },
        },
      },
    });

    if (!agent) {
      return res.status(404).json({
        error: "Agent not found",
      });
    }

    // Initialize RestGPT engine
    const engine = createRestGPTEngine({
      enableLogging: true,
    });

    if (!agent.apiSpec.openApiSpec) {
      return res.status(500).json({
        error: "Agent API spec is missing or invalid",
      });
    }

    const initResult = await engine.initialize(
      agent.apiSpec.openApiSpec as unknown as OpenAPISpec,
      agent.apiSpec.baseUrl,
    );

    if (initResult.success) {
      // Apply the agent's tool configuration – disable any tools not enabled by the agent
      initResult.tools.forEach((tool: FunctionTool) => {
        const matching = agent.tools.find(
          (at) => at.tool.name === tool.name && at.enabled,
        );
        // If tool isn't listed in agent config or disabled, disable it
        if (!matching) {
          engine.toggleTool(tool.id, false);
        }
      });
    }

    if (!initResult.success) {
      return res.status(500).json({
        error: "Failed to initialize agent engine",
      });
    }

    // Build conversation context including the agent's system prompt
    const conversationContext: ConversationContext = {
      sessionId: sessionId || `session_${Date.now()}`,
      userId:
        (session.user as { id?: string | null }).id ??
        session.user.email ??
        "anonymous",
      agentId,
      messageHistory: [
        {
          role: "system",
          content: agent.systemPrompt || "You are a helpful AI assistant.",
          timestamp: new Date(),
        },
        {
          role: "user",
          content: message,
          timestamp: new Date(),
        },
      ],
      executionHistory: [],
    };

    // Process the user request
    const result = await engine.processRequest(message, conversationContext);

    if (!result.success) {
      return res.status(500).json({
        error: "Failed to process request",
        message: result.error,
      });
    }

    // Save conversation to database
    let conversation = await prisma.agentConversation.findUnique({
      where: { sessionId: conversationContext.sessionId },
    });

    if (!conversation) {
      conversation = await prisma.agentConversation.create({
        data: {
          sessionId: conversationContext.sessionId,
          title: message.substring(0, 50) + (message.length > 50 ? "..." : ""),
          status: "ACTIVE",
          userId: (session.user as CustomUser).id,
          agentId,
        },
      });
    }

    // Save user message
    await prisma.agentMessage.create({
      data: {
        content: message,
        role: "USER",
        conversationId: conversation.id,
      },
    });

    // Save assistant response
    await prisma.agentMessage.create({
      data: {
        content: result.response,
        role: "ASSISTANT",
        conversationId: conversation.id,
        apiExecutions: result.executionHistory.map(
          (exec: { plan: APICallPlan; result: ExecutionResult }) => ({
            tool: exec.plan.toolId,
            success: exec.result.success,
            responseTime: exec.result.responseTime,
          }),
        ),
      },
    });

    // Update conversation stats
    await prisma.agentConversation.update({
      where: { id: conversation.id },
      data: {
        messageCount: { increment: 2 }, // user + assistant
        apiCallCount: { increment: result.executionHistory.length },
        lastMessageAt: new Date(),
      },
    });

    // Update agent stats
    await prisma.agent.update({
      where: { id: agentId },
      data: {
        messageCount: { increment: 2 },
        lastUsed: new Date(),
      },
    });

    res.status(200).json({
      success: true,
      data: {
        agentId,
        sessionId: conversationContext.sessionId,
        response: result.response,
        executionDetails: result.executionHistory.map(
          (exec: { plan: APICallPlan; result: ExecutionResult }) => ({
            tool: exec.plan.toolId,
            success: exec.result.success,
            responseTime: exec.result.responseTime,
          }),
        ),
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Chat error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
