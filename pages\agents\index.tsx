import { useRouter } from "next/router";

import { useState, useCallback } from "react";

import { useTeam } from "@/context/team-context";
import { 
  Bot, 
  Plus, 
  MessageCircle,
  Settings,
  Eye,
  MoreHorizontal,
  Calendar,
  Play,
  Pause,
  Trash2,
  Activity,
  Users,
  Wrench
} from "lucide-react";
import { toast } from "sonner";
import useS<PERSON> from "swr";

import AppLayout from "@/components/layouts/app";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertD<PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialog<PERSON>rigger,
} from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { fetcher, formatDate } from "@/lib/utils";

interface Agent {
  id: string;
  name: string;
  description: string;
  specId: string;
  specName: string;
  status: 'active' | 'paused' | 'error';
  toolCount: number;
  conversationCount: number;
  lastUsed: string | null;
  createdAt: string;
  createdBy: string;
  configuration: {
    model: string;
    temperature: number;
  };
}

interface AgentsResponse {
  success: boolean;
  data: {
    agents: Agent[];
    stats: {
      totalAgents: number;
      activeAgents: number;
      totalConversations: number;
      totalTools: number;
    };
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export default function AgentsPage() {
  const router = useRouter();
  const teamInfo = useTeam();
  const [deletingAgent, setDeletingAgent] = useState<string | null>(null);

  const { 
    data: agentsData, 
    error, 
    mutate: refreshAgents 
  } = useSWR<AgentsResponse>(
    teamInfo?.currentTeam?.id 
      ? `/api/agents?teamId=${teamInfo.currentTeam.id}`
      : null,
    fetcher,
    {
      refreshInterval: 30000, // Refresh every 30 seconds
    }
  );

  const handleCreateAgent = useCallback(() => {
    router.push('/agents/create');
  }, [router]);

  const handleChatWithAgent = useCallback((agentId: string) => {
    router.push(`/agents/${agentId}/chat`);
  }, [router]);

  const handleToggleAgentStatus = useCallback(async (agentId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'active' ? 'paused' : 'active';
    
    try {
      const response = await fetch(`/api/agents/${agentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          teamId: teamInfo?.currentTeam?.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update agent status');
      }

      refreshAgents();
      toast.success(`Agent ${newStatus === 'active' ? 'activated' : 'paused'} successfully`);
    } catch (error) {
      console.error('Toggle agent status error:', error);
      toast.error('Failed to update agent status');
    }
  }, [teamInfo?.currentTeam?.id, refreshAgents]);

  const handleDeleteAgent = useCallback(async (agentId: string) => {
    try {
      const response = await fetch(`/api/agents/${agentId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          teamId: teamInfo?.currentTeam?.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete agent');
      }

      refreshAgents();
      toast.success('Agent deleted successfully');
    } catch (error) {
      console.error('Delete agent error:', error);
      toast.error('Failed to delete agent');
    } finally {
      setDeletingAgent(null);
    }
  }, [teamInfo?.currentTeam?.id, refreshAgents]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-500';
      case 'paused':
        return 'text-yellow-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-muted-foreground';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Play className="h-4 w-4" />;
      case 'paused':
        return <Pause className="h-4 w-4" />;
      case 'error':
        return <Activity className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const agents = agentsData?.data?.agents || [];
  const stats = agentsData?.data?.stats;
  const isLoading = !agentsData && !error;

  return (
    <AppLayout>
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">AI Agents</h2>
            <p className="text-muted-foreground">
              Manage your AI agents and their conversational capabilities
            </p>
          </div>
          
          <Button 
            onClick={handleCreateAgent}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Agent
          </Button>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
                <Bot className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalAgents}</div>
                <p className="text-xs text-muted-foreground">
                  AI assistants created
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
                <Play className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.activeAgents}</div>
                <p className="text-xs text-muted-foreground">
                  Ready for conversations
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Conversations</CardTitle>
                <MessageCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalConversations}</div>
                <p className="text-xs text-muted-foreground">
                  Total chat sessions
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tools Available</CardTitle>
                <Wrench className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalTools}</div>
                <p className="text-xs text-muted-foreground">
                  Function tools deployed
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Agents Table */}
        <Card>
          <CardHeader>
            <CardTitle>Your AI Agents</CardTitle>
            <CardDescription>
              Created agents and their current status and usage statistics
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : agents.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Bot className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No agents created yet</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first AI agent to start automating API interactions
                </p>
                <Button 
                  onClick={handleCreateAgent}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Create Your First Agent
                </Button>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>API Specification</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Tools</TableHead>
                    <TableHead>Conversations</TableHead>
                    <TableHead>Last Used</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {agents.map((agent) => (
                    <TableRow 
                      key={agent.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleChatWithAgent(agent.id)}
                    >
                      <TableCell>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            <Bot className="h-4 w-4" />
                            {agent.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {agent.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{agent.specName}</div>
                          <div className="text-sm text-muted-foreground">
                            {agent.configuration.model}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className={`flex items-center gap-2 ${getStatusColor(agent.status)}`}>
                          {getStatusIcon(agent.status)}
                          <span className="text-sm capitalize">
                            {agent.status}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {agent.toolCount} tools
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <MessageCircle className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {agent.conversationCount}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {agent.lastUsed ? (
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            {formatDate(agent.lastUsed)}
                          </div>
                        ) : (
                          <span className="text-sm text-muted-foreground">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleChatWithAgent(agent.id)}>
                              <MessageCircle className="h-4 w-4 mr-2" />
                              Start Chat
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleAgentStatus(agent.id, agent.status)}>
                              {agent.status === 'active' ? (
                                <>
                                  <Pause className="h-4 w-4 mr-2" />
                                  Pause Agent
                                </>
                              ) : (
                                <>
                                  <Play className="h-4 w-4 mr-2" />
                                  Activate Agent
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => router.push(`/specs/${agent.specId}/tools`)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Tools
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem 
                                  className="text-red-600"
                                  onSelect={(e) => e.preventDefault()}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Agent
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Agent</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete "{agent.name}"? This action cannot be undone and will remove all associated conversation history.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteAgent(agent.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete Agent
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
