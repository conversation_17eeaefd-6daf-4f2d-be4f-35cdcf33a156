/**
 * API Route: List API specifications for a user/team
 * Replaces Papermark's document listing functionality
 */
import { NextApiRequest, NextApiResponse } from "next";

import {
  commonSchemas,
  createPaginatedResponse,
  parsePagination,
  requireAuth,
  validateMethod,
  validateQuery,
  withErrorHandling,
} from "@/lib/api-utils";
import prisma from "@/lib/prisma";
import { CustomUser } from "@/lib/types";

async function specsHandler(req: NextApiRequest, res: NextApiResponse) {
  validateMethod(req, ["GET"]);
  const session = await requireAuth(req, res);

  // Validate query parameters
  const query = validateQuery(
    req,
    commonSchemas.pagination.extend({
      teamId: commonSchemas.teamId,
    }),
  );

  const pagination = parsePagination(query);

  // Build where clause based on teamId
  const whereClause = query.teamId
    ? { teamId: query.teamId }
    : { ownerId: (session.user as CustomUser).id };

  // Get total count for pagination
  const totalCount = await prisma.apiSpecification.count({
    where: whereClause,
  });

  // Get paginated specs
  const specs = await prisma.apiSpecification.findMany({
    where: whereClause,
    select: {
      id: true,
      name: true,
      description: true,
      baseUrl: true,
      toolCount: true,
      enabled: true,
      status: true,
      createdAt: true,
      updatedAt: true,
      owner: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      team: {
        select: {
          id: true,
          name: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    skip: pagination.skip,
    take: pagination.limit,
  });

  console.log(
    `Found ${specs.length} specs for teamId: ${query.teamId}, totalCount: ${totalCount}`,
  );
  console.log(
    "Specs:",
    specs.map((s) => ({ id: s.id, name: s.name })),
  );

  res.status(200).json(createPaginatedResponse(specs, totalCount, pagination));
}

export default withErrorHandling(specsHandler);
