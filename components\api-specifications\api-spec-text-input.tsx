import { useState } from "react";

import { FileTextIcon } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { DocumentData, createDocument } from "@/lib/documents/create-document";
import { parseApiSpecification } from "@/lib/api-specifications/parse-api-spec";

interface ApiSpecTextInputProps {
  teamId: string;
  onSuccess: (apiSpecification: any) => void;
  onError: (error: string) => void;
  uploading: boolean;
  setUploading: (uploading: boolean) => void;
}

export function ApiSpecTextInput({
  teamId,
  onSuccess,
  onError,
  uploading,
  setUploading,
}: ApiSpecTextInputProps) {
  const [specText, setSpecText] = useState("");
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [baseUrl, setBaseUrl] = useState("");
  const [apiVersion, setApiVersion] = useState("");
  const [authType, setAuthType] = useState<string>("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!specText.trim()) {
      onError("Please provide the API specification content");
      return;
    }

    if (!name.trim()) {
      onError("Please provide a name for the API specification");
      return;
    }

    setUploading(true);

    try {
      // Detect content type based on the text content
      const trimmedText = specText.trim();
      let contentType = "application/json";
      
      // Simple detection: if it starts with '{' or '[', assume JSON, otherwise YAML
      if (!trimmedText.startsWith("{") && !trimmedText.startsWith("[")) {
        contentType = "application/x-yaml";
      }

      // Parse the specification
      const parsedSpec = await parseApiSpecification(specText, contentType);

      // Create a file for storage
      const fileName = `${name.replace(/[^a-zA-Z0-9]/g, "_")}.json`;
      const blob = new Blob([JSON.stringify(parsedSpec.schema, null, 2)], {
        type: "application/json",
      });
      const file = new File([blob], fileName, { type: "application/json" });

      // Upload the file to storage
      const { putFile } = await import("@/lib/files/put-file");
      const { type, data, fileSize } = await putFile({
        file,
        teamId,
      });

      if (!type || !data) {
        throw new Error("Failed to upload file");
      }

      // Create document
      const documentData: DocumentData = {
        name: fileName,
        key: data,
        storageType: type,
        contentType: "application/json",
        supportedFileType: "api",
        fileSize: fileSize,
      };

      const documentResponse = await fetch(`/api/teams/${teamId}/documents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: documentData.name,
          url: documentData.key,
          storageType: documentData.storageType,
          type: documentData.supportedFileType,
          contentType: documentData.contentType,
          fileSize: documentData.fileSize,
        }),
      });

      if (!documentResponse.ok) {
        throw new Error("Failed to create document");
      }

      const document = await documentResponse.json();

      // Create API specification
      const apiSpecResponse = await fetch(`/api/teams/${teamId}/api-specifications`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          documentId: document.id,
          name: name.trim(),
          description: description.trim() || undefined,
          baseUrl: baseUrl.trim() || parsedSpec.baseUrl || undefined,
          apiVersion: apiVersion.trim() || parsedSpec.version || undefined,
          authType: authType || parsedSpec.authType || undefined,
          authConfig: parsedSpec.authConfig || undefined,
          parsedSchema: parsedSpec.schema || undefined,
          functionTools: parsedSpec.functionTools || [],
        }),
      });

      if (!apiSpecResponse.ok) {
        throw new Error("Failed to create API specification");
      }

      const apiSpecification = await apiSpecResponse.json();
      onSuccess(apiSpecification.data);
    } catch (error) {
      console.error("Error creating API specification:", error);
      onError(error instanceof Error ? error.message : "Failed to create API specification");
    } finally {
      setUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Specification Text */}
      <div className="space-y-2">
        <Label htmlFor="specText">API Specification *</Label>
        <div className="relative">
          <Textarea
            id="specText"
            value={specText}
            onChange={(e) => setSpecText(e.target.value)}
            placeholder={`Paste your OpenAPI/Swagger specification here...

Example (JSON):
{
  "openapi": "3.0.0",
  "info": {
    "title": "My API",
    "version": "1.0.0"
  },
  "paths": {
    "/users": {
      "get": {
        "summary": "List users"
      }
    }
  }
}

Example (YAML):
openapi: 3.0.0
info:
  title: My API
  version: 1.0.0
paths:
  /users:
    get:
      summary: List users`}
            rows={12}
            className="font-mono text-sm"
            required
          />
        </div>
        <p className="text-xs text-muted-foreground">
          Paste your OpenAPI 3.x or Swagger 2.x specification in JSON or YAML format
        </p>
      </div>

      {/* Metadata */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name *</Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="My API v1"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="apiVersion">API Version</Label>
          <Input
            id="apiVersion"
            value={apiVersion}
            onChange={(e) => setApiVersion(e.target.value)}
            placeholder="1.0.0"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Brief description of your API"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="baseUrl">Base URL</Label>
          <Input
            id="baseUrl"
            value={baseUrl}
            onChange={(e) => setBaseUrl(e.target.value)}
            placeholder="https://api.example.com/v1"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="authType">Authentication Type</Label>
          <Select value={authType} onValueChange={setAuthType}>
            <SelectTrigger>
              <SelectValue placeholder="Select auth type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="NONE">None</SelectItem>
              <SelectItem value="API_KEY">API Key</SelectItem>
              <SelectItem value="BEARER_TOKEN">Bearer Token</SelectItem>
              <SelectItem value="BASIC_AUTH">Basic Auth</SelectItem>
              <SelectItem value="OAUTH2">OAuth 2.0</SelectItem>
              <SelectItem value="CUSTOM">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit" disabled={uploading || !specText.trim() || !name.trim()}>
          {uploading ? "Creating..." : "Create API Specification"}
        </Button>
      </div>
    </form>
  );
}
