/**
 * API Route: List AI Agents for a user/team
 * Manages the collection of created agents
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import prisma from "@/lib/prisma";
import { CustomUser } from "@/lib/types";

import { authOptions } from "../auth/[...nextauth]";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const { teamId, page = "1", limit = "10" } = req.query;

    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const skip = (pageNum - 1) * limitNum;

    // Build where clause based on teamId
    const whereClause = teamId
      ? { teamId: teamId as string }
      : { ownerId: (session.user as CustomUser).id };

    // Get total count for pagination
    const totalCount = await prisma.agent.count({
      where: whereClause,
    });

    // Get paginated agents
    const agents = await prisma.agent.findMany({
      where: whereClause,
      include: {
        apiSpec: {
          select: {
            id: true,
            name: true,
          },
        },
        tools: {
          select: {
            id: true,
          },
        },
        conversations: {
          select: {
            id: true,
          },
        },
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limitNum,
    });

    // Format agents for response
    const formattedAgents = agents.map((agent) => ({
      id: agent.id,
      name: agent.name,
      description: agent.description,
      specId: agent.apiSpecId,
      specName: agent.apiSpec.name,
      status: agent.status.toLowerCase(),
      toolCount: agent.tools.length,
      conversationCount: agent.conversationCount,
      lastUsed: agent.lastUsed?.toISOString() || null,
      createdAt: agent.createdAt.toISOString(),
      createdBy: agent.ownerId,
      configuration: agent.modelConfig,
    }));

    // Calculate summary statistics
    const stats = {
      totalAgents: totalCount,
      activeAgents: agents.filter((a) => a.status === "ACTIVE").length,
      totalConversations: agents.reduce(
        (sum, a) => sum + a.conversationCount,
        0,
      ),
      totalTools: agents.reduce((sum, a) => sum + a.tools.length, 0),
    };

    res.status(200).json({
      success: true,
      data: {
        agents: formattedAgents,
        stats,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limitNum),
        },
      },
    });
  } catch (error) {
    console.error("List agents error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
