import { useRouter } from "next/router";

import { useState, useC<PERSON>back, useRef, useEffect } from "react";

import { useTeam } from "@/context/team-context";
import { 
  ArrowLeft,
  Send,
  Bot,
  User,
  Copy,
  RotateCcw,
  Download,
  Settings,
  AlertCircle,
  CheckCircle,
  Loader,
  Play,
  Square
} from "lucide-react";
import { toast } from "sonner";
import useSWR from "swr";

import AppLayout from "@/components/layouts/app";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";

import { fetcher } from "@/lib/utils";

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  toolCalls?: ToolCall[];
  error?: string;
}

interface ToolCall {
  id: string;
  name: string;
  arguments: string;
  result?: string;
  error?: string;
  status: 'pending' | 'success' | 'error';
}

interface Agent {
  id: string;
  name: string;
  description: string;
  specId: string;
  specName: string;
  status: 'active' | 'paused' | 'error';
  toolCount: number;
  conversationCount: number;
  configuration: {
    model: string;
    temperature: number;
  };
}

interface ChatResponse {
  success: boolean;
  data: {
    agent: Agent;
    messages: Message[];
    conversation: {
      id: string;
      title: string;
      createdAt: string;
      updatedAt: string;
    };
  };
}

export default function AgentChatPage() {
  const router = useRouter();
  const { id: agentId } = router.query;
  const teamInfo = useTeam();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showAgentInfo, setShowAgentInfo] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const { 
    data: chatData, 
    error,
    mutate: refreshChat
  } = useSWR<ChatResponse>(
    agentId && teamInfo?.currentTeam?.id 
      ? `/api/agents/${agentId}/chat?teamId=${teamInfo.currentTeam.id}`
      : null,
    fetcher
  );

  const agent = chatData?.data?.agent;
  const conversation = chatData?.data?.conversation;

  // Initialize messages when data loads
  useEffect(() => {
    if (chatData?.data?.messages) {
      setMessages(chatData.data.messages);
    }
  }, [chatData]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleBackToAgents = useCallback(() => {
    router.push('/agents');
  }, [router]);

  const handleSendMessage = useCallback(async () => {
    if (!input.trim() || isLoading || !agentId) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(`/api/agents/${agentId}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: input.trim(),
          conversationId: conversation?.id,
          teamId: teamInfo?.currentTeam?.id,
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response stream');

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString(),
        toolCalls: [],
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Read the stream
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'content') {
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessage.id 
                    ? { ...msg, content: msg.content + data.content }
                    : msg
                ));
              } else if (data.type === 'tool_call') {
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessage.id 
                    ? { 
                        ...msg, 
                        toolCalls: [...(msg.toolCalls || []), {
                          id: data.tool_call.id,
                          name: data.tool_call.function.name,
                          arguments: data.tool_call.function.arguments,
                          status: 'pending' as const,
                        }]
                      }
                    : msg
                ));
              } else if (data.type === 'tool_result') {
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessage.id 
                    ? {
                        ...msg,
                        toolCalls: msg.toolCalls?.map(tool =>
                          tool.id === data.tool_call_id
                            ? { 
                                ...tool, 
                                result: data.result,
                                status: data.error ? 'error' : 'success',
                                error: data.error
                              }
                            : tool
                        )
                      }
                    : msg
                ));
              }
            } catch (e) {
              console.error('Failed to parse stream data:', e);
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Request aborted');
        return;
      }
      
      console.error('Send message error:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        role: 'assistant',
        content: `Sorry, I encountered an error while processing your request: ${error.message}. Please try again.`,
        timestamp: new Date().toISOString(),
        error: error.message,
      };

      setMessages(prev => [...prev, errorMessage]);
      toast.error('Failed to send message');
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [input, isLoading, agentId, conversation?.id, teamInfo?.currentTeam?.id]);

  const handleStopGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsLoading(false);
    }
  }, []);

  const handleCopyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Message copied to clipboard');
  }, []);

  const handleClearChat = useCallback(() => {
    setMessages([]);
    toast.success('Chat cleared');
  }, []);

  const handleExportChat = useCallback(() => {
    const chatData = {
      agent: agent?.name,
      conversation: conversation?.title,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp,
        toolCalls: msg.toolCalls
      })),
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${agent?.name || 'agent'}-chat-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Chat exported successfully');
  }, [agent?.name, conversation?.title, messages]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-medium mb-2">Error loading agent</h3>
          <p className="text-muted-foreground mb-4">
            Failed to load agent or conversation. Please try again.
          </p>
          <Button onClick={handleBackToAgents} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Agents
          </Button>
        </div>
      </AppLayout>
    );
  }

  if (!agent) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex flex-col h-[calc(100vh-4rem)]">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-background">
          <div className="flex items-center gap-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleBackToAgents}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarFallback>
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="font-semibold">{agent.name}</h2>
                <p className="text-sm text-muted-foreground">
                  {agent.specName} • {agent.toolCount} tools
                </p>
              </div>
              <Badge 
                variant={agent.status === 'active' ? 'default' : 'secondary'}
                className="ml-2"
              >
                {agent.status}
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearChat}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Clear
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportChat}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Dialog open={showAgentInfo} onOpenChange={setShowAgentInfo}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Agent Information</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium">Description</h4>
                    <p className="text-sm text-muted-foreground">{agent.description}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">Configuration</h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>Model: {agent.configuration.model}</p>
                      <p>Temperature: {agent.configuration.temperature}</p>
                      <p>Tools: {agent.toolCount}</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium">Statistics</h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>Conversations: {agent.conversationCount}</p>
                      <p>API Specification: {agent.specName}</p>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Messages */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4 max-w-4xl mx-auto">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Start a conversation</h3>
                <p className="text-muted-foreground">
                  Ask your AI agent anything. It can use {agent.toolCount} tools from {agent.specName}.
                </p>
              </div>
            ) : (
              messages.map((message) => (
                <div 
                  key={message.id}
                  className={`flex gap-3 ${
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  {message.role !== 'user' && (
                    <Avatar>
                      <AvatarFallback>
                        <Bot className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                  
                  <div className={`max-w-[70%] ${
                    message.role === 'user' ? 'order-first' : ''
                  }`}>
                    <Card className={`${
                      message.role === 'user' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted'
                    }`}>
                      <CardContent className="p-3">
                        <div className="whitespace-pre-wrap text-sm">
                          {message.content}
                        </div>
                        
                        {/* Tool Calls */}
                        {message.toolCalls && message.toolCalls.length > 0 && (
                          <div className="mt-3 space-y-2">
                            <Separator />
                            <div className="text-xs font-medium opacity-70">
                              API Calls:
                            </div>
                            {message.toolCalls.map((toolCall) => (
                              <div key={toolCall.id} className="text-xs space-y-1">
                                <div className="flex items-center gap-2">
                                  {toolCall.status === 'pending' && (
                                    <Loader className="h-3 w-3 animate-spin" />
                                  )}
                                  {toolCall.status === 'success' && (
                                    <CheckCircle className="h-3 w-3 text-green-500" />
                                  )}
                                  {toolCall.status === 'error' && (
                                    <AlertCircle className="h-3 w-3 text-red-500" />
                                  )}
                                  <span className="font-mono">{toolCall.name}</span>
                                </div>
                                {toolCall.result && (
                                  <div className="ml-5 p-2 bg-background/50 rounded text-xs font-mono">
                                    {toolCall.result}
                                  </div>
                                )}
                                {toolCall.error && (
                                  <div className="ml-5 p-2 bg-red-500/20 rounded text-xs">
                                    Error: {toolCall.error}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs opacity-50">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyMessage(message.content)}
                            className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  
                  {message.role === 'user' && (
                    <Avatar>
                      <AvatarFallback>
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))
            )}
            
            {isLoading && (
              <div className="flex gap-3 justify-start">
                <Avatar>
                  <AvatarFallback>
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <Card className="bg-muted">
                  <CardContent className="p-3">
                    <div className="flex items-center gap-2 text-sm">
                      <Loader className="h-4 w-4 animate-spin" />
                      Thinking...
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input */}
        <div className="p-4 border-t bg-background">
          <div className="max-w-4xl mx-auto">
            <div className="flex gap-2">
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={`Message ${agent.name}...`}
                className="min-h-[50px] max-h-32 resize-none"
                disabled={isLoading || agent.status !== 'active'}
              />
              {isLoading ? (
                <Button 
                  onClick={handleStopGeneration}
                  variant="outline"
                  size="icon"
                  className="self-end"
                >
                  <Square className="h-4 w-4" />
                </Button>
              ) : (
                <Button 
                  onClick={handleSendMessage}
                  disabled={!input.trim() || agent.status !== 'active'}
                  size="icon"
                  className="self-end"
                >
                  <Send className="h-4 w-4" />
                </Button>
              )}
            </div>
            
            {agent.status !== 'active' && (
              <p className="text-sm text-muted-foreground mt-2 text-center">
                Agent is {agent.status}. Please activate it to start chatting.
              </p>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
