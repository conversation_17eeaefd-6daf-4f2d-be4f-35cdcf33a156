import { useRouter } from "next/router";

import { useState, use<PERSON><PERSON>back } from "react";

import { useTeam } from "@/context/team-context";
import { 
  ArrowLeft,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Code,
  Eye,
  EyeOff,
  Edit,
  Bot,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import useS<PERSON> from "swr";

import AppLayout from "@/components/layouts/app";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter 
} from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

import { fetcher } from "@/lib/utils";

interface ToolParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  example?: string;
}

interface Tool {
  id: string;
  name: string;
  description: string;
  method: string;
  path: string;
  enabled: boolean;
  parameters: ToolParameter[];
  responseSchema?: object;
  tags: string[];
}

interface ApiSpec {
  id: string;
  name: string;
  description: string;
  baseUrl: string;
  version: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ToolsResponse {
  success: boolean;
  data: {
    spec: ApiSpec;
    tools: Tool[];
    stats: {
      totalTools: number;
      enabledTools: number;
      endpointsCovered: number;
    };
  };
}

export default function SpecToolsPage() {
  const router = useRouter();
  const { id: specId } = router.query;
  const teamInfo = useTeam();
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [editingTool, setEditingTool] = useState<Tool | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  const { 
    data: toolsData, 
    error, 
    mutate: refreshTools 
  } = useSWR<ToolsResponse>(
    specId && teamInfo?.currentTeam?.id 
      ? `/api/tools/${specId}?teamId=${teamInfo.currentTeam.id}`
      : null,
    fetcher
  );

  const handleBackToSpecs = useCallback(() => {
    router.push('/specs');
  }, [router]);

  const handleCreateAgent = useCallback(() => {
    router.push(`/agents/create?specId=${specId}`);
  }, [router, specId]);

  const handleToggleTool = useCallback(async (toolId: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/tools/${specId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          toolId,
          enabled,
          teamId: teamInfo?.currentTeam?.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to toggle tool');
      }

      refreshTools();
      toast.success(`Tool ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Toggle tool error:', error);
      toast.error('Failed to update tool status');
    }
  }, [specId, teamInfo?.currentTeam?.id, refreshTools]);

  const handleEditTool = useCallback((tool: Tool) => {
    setEditingTool({ ...tool });
    setShowEditDialog(true);
  }, []);

  const handleSaveToolEdit = useCallback(async () => {
    if (!editingTool) return;

    try {
      const response = await fetch(`/api/tools/${specId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          toolId: editingTool.id,
          name: editingTool.name,
          description: editingTool.description,
          teamId: teamInfo?.currentTeam?.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update tool');
      }

      refreshTools();
      setShowEditDialog(false);
      setEditingTool(null);
      toast.success('Tool updated successfully');
    } catch (error) {
      console.error('Update tool error:', error);
      toast.error('Failed to update tool');
    }
  }, [editingTool, specId, teamInfo?.currentTeam?.id, refreshTools]);

  const handleRegenerateTools = useCallback(async () => {
    try {
      const response = await fetch(`/api/tools/${specId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          teamId: teamInfo?.currentTeam?.id,
        }),
      });

      if (!response.ok) {
        // Try to extract error message from response
        let errorMsg = 'Failed to regenerate tools';
        try {
          const errJson = await response.json();
          if (errJson?.error) errorMsg = errJson.error;
          if (errJson?.details) errorMsg += `: ${JSON.stringify(errJson.details)}`;
        } catch {}
        throw new Error(errorMsg);
      }

      refreshTools();
      toast.success('Tools regenerated successfully');
    } catch (error) {
      console.error('Regenerate tools error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to regenerate tools');
    }
  }, [specId, teamInfo?.currentTeam?.id, refreshTools]);

  const spec = toolsData?.data?.spec;
  const tools = toolsData?.data?.tools || [];
  const stats = toolsData?.data?.stats;
  const isLoading = !toolsData && !error;

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-medium mb-2">Error loading tools</h3>
          <p className="text-muted-foreground mb-4">
            Failed to load API specification tools. Please try again.
          </p>
          <Button onClick={handleBackToSpecs} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Specs
          </Button>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleBackToSpecs}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Specs
            </Button>
            <div>
              <h2 className="text-3xl font-bold tracking-tight">
                {spec?.name || 'Loading...'}
              </h2>
              <p className="text-muted-foreground">
                Manage function tools generated from this API specification
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              onClick={handleRegenerateTools}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Regenerate Tools
            </Button>
            <Button 
              onClick={handleCreateAgent}
              className="flex items-center gap-2"
            >
              <Bot className="h-4 w-4" />
              Create Agent
            </Button>
          </div>
        </div>

        {/* API Info Card */}
        {spec && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    {spec.name}
                  </CardTitle>
                  <CardDescription>{spec.description}</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  {spec.enabled ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <span className="text-sm">
                    {spec.enabled ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div>
                  <Label className="text-sm font-medium">Base URL</Label>
                  <p className="font-mono text-sm text-muted-foreground">{spec.baseUrl}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Version</Label>
                  <p className="text-sm text-muted-foreground">{spec.version}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Updated</Label>
                  <p className="text-sm text-muted-foreground">
                    {new Date(spec.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tools</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalTools}</div>
                <p className="text-xs text-muted-foreground">
                  Generated function tools
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Enabled Tools</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.enabledTools}</div>
                <p className="text-xs text-muted-foreground">
                  Ready for agent use
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Coverage</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.endpointsCovered}</div>
                <p className="text-xs text-muted-foreground">
                  API endpoints covered
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Tools List */}
        <Card>
          <CardHeader>
            <CardTitle>Function Tools</CardTitle>
            <CardDescription>
              Generated tools from your API specification. Toggle to enable/disable for agent use.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : tools.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Settings className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No tools generated</h3>
                <p className="text-muted-foreground mb-4">
                  Tools haven't been generated from this API specification yet
                </p>
                <Button 
                  onClick={handleRegenerateTools}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Generate Tools
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {tools.map((tool) => (
                  <Collapsible key={tool.id}>
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4 flex-1">
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={tool.enabled}
                            onCheckedChange={(enabled) => handleToggleTool(tool.id, enabled)}
                          />
                          {tool.enabled ? (
                            <Eye className="h-4 w-4 text-green-500" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-muted-foreground" />
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{tool.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {tool.endpoint.method.toUpperCase()}
                            </Badge>
                            {/* {tool.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))} */}
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">
                            {tool.description}
                          </p>
                          <p className="font-mono text-xs text-muted-foreground">
                            {tool.path}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <CollapsibleTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </CollapsibleTrigger>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleEditTool(tool)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <CollapsibleContent className="px-4 pb-4">
                      <Tabs defaultValue="parameters" className="mt-4">
                        <TabsList>
                          <TabsTrigger value="parameters">Parameters</TabsTrigger>
                          <TabsTrigger value="response">Response</TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="parameters" className="mt-4">
                          {tool.parameters.length > 0 ? (
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Name</TableHead>
                                  <TableHead>Type</TableHead>
                                  <TableHead>Required</TableHead>
                                  <TableHead>Description</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {tool.parameters.map((param) => (
                                  <TableRow key={param.name}>
                                    <TableCell className="font-mono text-sm">
                                      {param.name}
                                    </TableCell>
                                    <TableCell>
                                      <Badge variant="outline">{param.type}</Badge>
                                    </TableCell>
                                    <TableCell>
                                      {param.required ? (
                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                      ) : (
                                        <XCircle className="h-4 w-4 text-muted-foreground" />
                                      )}
                                    </TableCell>
                                    <TableCell className="text-sm">
                                      {param.description}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          ) : (
                            <p className="text-sm text-muted-foreground">No parameters</p>
                          )}
                        </TabsContent>
                        
                        <TabsContent value="response" className="mt-4">
                          <div className="bg-muted p-4 rounded-lg">
                            <pre className="text-sm overflow-auto">
                              {JSON.stringify(tool.responseSchema, null, 2)}
                            </pre>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </CollapsibleContent>
                  </Collapsible>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Edit Tool Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Tool</DialogTitle>
          </DialogHeader>
          {editingTool && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="tool-name">Name</Label>
                <Input
                  id="tool-name"
                  value={editingTool.name}
                  onChange={(e) => setEditingTool({
                    ...editingTool,
                    name: e.target.value
                  })}
                />
              </div>
              <div>
                <Label htmlFor="tool-description">Description</Label>
                <Textarea
                  id="tool-description"
                  value={editingTool.description}
                  onChange={(e) => setEditingTool({
                    ...editingTool,
                    description: e.target.value
                  })}
                  rows={3}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowEditDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleSaveToolEdit}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
