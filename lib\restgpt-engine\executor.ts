/**
 * Executor Component - Executes API calls and parses responses
 * Inspired by RestGPT's Executor with Caller and Parser sub-modules
 */
import { Agent, run } from "@openai/agents";

import {
  APICallPlan,
  EngineConfig,
  ExecutionResult,
  FunctionTool,
} from "./types";

export class APIExecutor {
  private callerAgent: Agent | null = null;
  private parserAgent: Agent | null = null;

  constructor(private config: EngineConfig) {}

  /**
   * Initialize the executor agents
   */
  async initialize(availableTools: FunctionTool[]): Promise<void> {
    // Create Caller agent for API parameter generation
    this.callerAgent = new Agent({
      name: "api-caller-agent",
      model: "",
      instructions: this.buildCallerSystemPrompt(),
    });

    // Create Parser agent for response parsing
    this.parserAgent = new Agent({
      name: "api-parser-agent",
      model: "gpt-4.1-nano",
      instructions: this.buildParserSystemPrompt(),
    });
  }

  /**
   * Execute an API call plan
   */
  async executeAPICall(
    plan: APICallPlan,
    baseUrl: string,
    userContext: Record<string, any> = {},
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      // Step 1: Generate API call parameters using Caller agent
      const callParams = await this.generateCallParameters(plan, userContext);

      // Step 2: Make the actual HTTP request
      const response = await this.makeHttpRequest(plan, callParams, baseUrl);

      // Step 3: Parse the response using Parser agent
      const parsedOutput = await this.parseResponse(response, plan);

      const responseTime = Date.now() - startTime;

      return {
        success: true,
        data: response.data,
        statusCode: response.status,
        responseTime,
        parsedOutput,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        responseTime,
      };
    }
  }

  /**
   * Generate API call parameters using Caller agent
   */
  private async generateCallParameters(
    plan: APICallPlan,
    userContext: Record<string, any>,
  ): Promise<{
    url: string;
    method: string;
    headers: Record<string, string>;
    queryParams: Record<string, any>;
    body?: any;
  }> {
    if (!this.callerAgent) {
      throw new Error("Caller agent not initialized");
    }

    const callerPrompt = this.buildCallerPrompt(plan, userContext);

    try {
      const result = await run(this.callerAgent, callerPrompt);

      if (result.finalOutput) {
        return this.parseCallerResponse(result.finalOutput);
      }

      throw new Error("Failed to generate call parameters");
    } catch (error) {
      console.error("Caller error:", error);
      throw new Error(
        `Parameter generation failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Make HTTP request to API
   */
  private async makeHttpRequest(
    plan: APICallPlan,
    callParams: any,
    baseUrl: string,
  ): Promise<{ data: any; status: number; headers: any }> {
    const url = new URL(callParams.url, baseUrl);

    // Add query parameters
    Object.entries(callParams.queryParams || {}).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });

    const requestOptions: RequestInit = {
      method: callParams.method,
      headers: {
        "Content-Type": "application/json",
        ...callParams.headers,
      },
      signal: AbortSignal.timeout(this.config.timeout || 30000),
    };

    if (
      callParams.body &&
      ["POST", "PUT", "PATCH"].includes(callParams.method)
    ) {
      requestOptions.body = JSON.stringify(callParams.body);
    }

    const response = await fetch(url.toString(), requestOptions);

    let data;
    try {
      data = await response.json();
    } catch {
      data = await response.text();
    }

    if (!response.ok) {
      throw new Error(
        `HTTP ${response.status}: ${response.statusText} - ${JSON.stringify(data)}`,
      );
    }

    return {
      data,
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
    };
  }

  /**
   * Parse API response using Parser agent
   */
  private async parseResponse(
    response: { data: any; status: number; headers: any },
    plan: APICallPlan,
  ): Promise<any> {
    if (!this.parserAgent) {
      throw new Error("Parser agent not initialized");
    }

    const parserPrompt = this.buildParserPrompt(response, plan);

    try {
      const result = await run(this.parserAgent, parserPrompt);

      if (result.finalOutput) {
        return this.parseParserResponse(result.finalOutput);
      }

      throw new Error("Failed to parse response");
    } catch (error) {
      console.error("Parser error:", error);
      // Return raw data if parsing fails
      return response.data;
    }
  }

  /**
   * Build system prompt for Caller agent
   */
  private buildCallerSystemPrompt(): string {
    return `You are an API Caller agent. Your role is to generate correct parameters and request bodies for API calls based on API documentation and user context.

Your responsibilities:
1. Read API endpoint documentation carefully
2. Generate correct URL paths with path parameters
3. Create appropriate query parameters
4. Build request bodies for POST/PUT/PATCH requests
5. Set necessary headers

Guidelines:
- Always validate required parameters
- Use appropriate data types
- Handle path parameter substitution
- Consider authentication requirements
- Provide clear error messages for missing data

Response Format:
Always respond with a JSON object:
{
  "url": "endpoint path with path params substituted",
  "method": "HTTP method",
  "headers": {"key": "value"},
  "queryParams": {"key": "value"},
  "body": {} // only for POST/PUT/PATCH
}`;
  }

  /**
   * Build system prompt for Parser agent
   */
  private buildParserSystemPrompt(): string {
    return `You are an API Response Parser agent. Your role is to extract meaningful information from API responses based on the expected output goals.

Your responsibilities:
1. Parse JSON responses according to schema information
2. Extract relevant data based on output instructions
3. Handle different response formats gracefully
4. Provide structured output for further processing
5. Identify and report parsing errors

Guidelines:
- Focus on extracting information that matches the output goal
- Maintain data structure and relationships
- Handle missing or unexpected fields gracefully
- Provide clear, actionable extracted data
- Use the response schema as a guide

Response Format:
Always respond with extracted data in a structured format that matches the output goal.`;
  }

  /**
   * Build prompt for Caller agent
   */
  private buildCallerPrompt(
    plan: APICallPlan,
    userContext: Record<string, any>,
  ): string {
    const endpoint = plan.endpoint;

    return `Generate API call parameters for the following endpoint:

ENDPOINT: ${endpoint.method} ${endpoint.path}
DESCRIPTION: ${endpoint.description || endpoint.summary || "No description"}

PARAMETERS:
${JSON.stringify(endpoint.parameters || [], null, 2)}

REQUEST BODY:
${JSON.stringify(endpoint.requestBody || {}, null, 2)}

USER PROVIDED PARAMETERS:
${JSON.stringify(plan.parameters, null, 2)}

USER CONTEXT:
${JSON.stringify(userContext, null, 2)}

Please generate the complete API call parameters including URL, method, headers, query parameters, and request body.`;
  }

  /**
   * Build prompt for Parser agent
   */
  private buildParserPrompt(
    response: { data: any; status: number; headers: any },
    plan: APICallPlan,
  ): string {
    return `Parse the following API response:

RESPONSE STATUS: ${response.status}
RESPONSE DATA:
${JSON.stringify(response.data, null, 2)}

OUTPUT GOAL: ${plan.expectedResponse}
PARSING INSTRUCTIONS: ${plan.outputInstruction}

ENDPOINT SCHEMA:
${JSON.stringify(plan.endpoint.responses, null, 2)}

Please extract the relevant information according to the output goal and parsing instructions.`;
  }

  /**
   * Parse Caller agent response
   */
  private parseCallerResponse(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No JSON found in caller response");
      }
      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      throw new Error(
        `Failed to parse caller response: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Parse Parser agent response
   */
  private parseParserResponse(response: string): any {
    try {
      // Try to parse as JSON first
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      // Return as text if not JSON
      return { extractedText: response };
    } catch (error) {
      return { extractedText: response };
    }
  }
}
