import { useRouter } from "next/router";
import { useState, useCallback } from "react";

import { useTeam } from "@/context/team-context";
import { 
  ArrowLeft,
  Settings,
  Bot,
  Code,
  CheckCircle,
  XCircle,
  AlertCircle,
  ExternalLink,
  Calendar,
  Globe,
  FileText,
  Wrench
} from "lucide-react";
import { toast } from "sonner";
import useS<PERSON> from "swr";

import AppLayout from "@/components/layouts/app";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { fetcher } from "@/lib/utils";

interface ApiSpec {
  id: string;
  name: string;
  description: string;
  baseUrl: string;
  version: string;
  enabled: boolean;
  status: string;
  toolCount: number;
  createdAt: string;
  updatedAt: string;
  openApiSpec: any;
}

interface SpecResponse {
  success: boolean;
  data: ApiSpec;
}

export default function SpecDetailPage() {
  const router = useRouter();
  const { id: specId } = router.query;
  const teamInfo = useTeam();

  const { 
    data: specData, 
    error, 
    mutate: refreshSpec 
  } = useSWR<SpecResponse>(
    specId && teamInfo?.currentTeam?.id 
      ? `/api/specs/${specId}?teamId=${teamInfo.currentTeam.id}`
      : null,
    fetcher
  );

  const handleBackToSpecs = useCallback(() => {
    router.push('/specs');
  }, [router]);

  const handleViewTools = useCallback(() => {
    router.push(`/specs/${specId}/tools`);
  }, [router, specId]);

  const handleCreateAgent = useCallback(() => {
    router.push(`/agents/create?specId=${specId}`);
  }, [router, specId]);

  const spec = specData?.data;
  const isLoading = !specData && !error;

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-medium mb-2">Error loading specification</h3>
          <p className="text-muted-foreground mb-4">
            Failed to load API specification. Please try again.
          </p>
          <Button onClick={handleBackToSpecs} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Specs
          </Button>
        </div>
      </AppLayout>
    );
  }

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleBackToSpecs}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Specs
            </Button>
            <div>
              <h2 className="text-3xl font-bold tracking-tight">
                {spec?.name || 'Loading...'}
              </h2>
              <p className="text-muted-foreground">
                API specification details and management
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              onClick={handleViewTools}
              className="flex items-center gap-2"
            >
              <Wrench className="h-4 w-4" />
              Manage Tools
            </Button>
            <Button 
              onClick={handleCreateAgent}
              className="flex items-center gap-2"
            >
              <Bot className="h-4 w-4" />
              Create Agent
            </Button>
          </div>
        </div>

        {/* Main Content */}
        {spec && (
          <div className="space-y-6">
            {/* Overview Card */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Code className="h-5 w-5" />
                      {spec.name}
                    </CardTitle>
                    <CardDescription>{spec.description}</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    {spec.enabled ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <Badge variant={spec.enabled ? "default" : "secondary"}>
                      {spec.enabled ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Base URL</span>
                    </div>
                    <p className="font-mono text-sm text-muted-foreground break-all">
                      {spec.baseUrl}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Version</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{spec.version}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Wrench className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Function Tools</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{spec.toolCount} tools</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Created</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {new Date(spec.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="grid gap-4 md:grid-cols-2">
              <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleViewTools}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wrench className="h-5 w-5" />
                    Function Tools
                  </CardTitle>
                  <CardDescription>
                    View and manage the {spec.toolCount} function tools generated from this specification
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    Manage Tools
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleCreateAgent}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bot className="h-5 w-5" />
                    AI Agent
                  </CardTitle>
                  <CardDescription>
                    Create an AI agent that can use the tools from this API specification
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">
                    Create Agent
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* OpenAPI Specification */}
            <Card>
              <CardHeader>
                <CardTitle>OpenAPI Specification</CardTitle>
                <CardDescription>
                  Raw OpenAPI/Swagger specification content
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-lg max-h-96 overflow-auto">
                  <pre className="text-sm">
                    {JSON.stringify(spec.openApiSpec, null, 2)}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
