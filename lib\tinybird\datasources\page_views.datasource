VERSION 3

DESCRIPTION >
  Page views are events when a user views a document

SCHEMA >
  `id` String `json:$.id`,
  `linkId` String `json:$.linkId`,
  `documentId` String `json:$.documentId`,
  `viewId` String `json:$.viewId`,
  `dataroomId` Nullable(String) `json:$.dataroomId`,
  `versionNumber` UInt16 `json:$.versionNumber`,
  # Unix timestamp
  `time` Int64 `json:$.time`,
  `duration` UInt32 `json:$.duration`,
  # The page number
  `pageNumber` LowCardinality(String) `json:$.pageNumber`,
  `country` String `json:$.country`,
  `city` String `json:$.city`,
  `region` String `json:$.region`,
  `latitude` String `json:$.latitude`,
  `longitude` String `json:$.longitude`,
  `ua` String `json:$.ua`,
  `browser` String `json:$.browser`,
  `browser_version` String `json:$.browser_version`,
  `engine` String `json:$.engine`,
  `engine_version` String `json:$.engine_version`,
  `os` String `json:$.os`,
  `os_version` String `json:$.os_version`,
  `device` String `json:$.device`,
  `device_vendor` String `json:$.device_vendor`,
  `device_model` String `json:$.device_model`,
  `cpu_architecture` String `json:$.cpu_architecture`,
  `bot` UInt8 `json:$.bot`,
  `referer` String `json:$.referer`,
  `referer_url` String `json:$.referer_url`


ENGINE "MergeTree"
ENGINE_SORTING_KEY "linkId,documentId,viewId,versionNumber,pageNumber,time,id"

