import React from "react";

function ProductHuntIcon(
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) {
  return (
    <svg viewBox="0 0 24 24" fill="none" {...props}>
      <g clipPath="url(#clip0_9_7)">
        <path
          d="M15.2955 10.2563C15.2955 11.217 14.5125 12 13.5518 12H10.2563V8.5125H13.5518C14.5125 8.5125 15.2955 9.2955 15.2955 10.2563ZM23.625 12C23.625 18.4222 18.4215 23.625 12 23.625C5.5785 23.625 0.375 18.4215 0.375 12C0.375 5.57775 5.5785 0.375 12 0.375C18.4215 0.375 23.625 5.5785 23.625 12ZM17.6205 10.2563C17.6205 8.01075 15.7973 6.1875 13.5518 6.1875H7.93125V17.8125H10.2563V14.325H13.5518C15.7973 14.325 17.6205 12.5018 17.6205 10.2563Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_9_7">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default ProductHuntIcon;
