// API2Agent Models - New models for API specification management and AI agents
// These extend the existing Papermark schema with API-to-agent functionality

model ApiSpecification {
  id          String  @id @default(cuid())
  name        String
  description String?
  baseUrl     String

  // OpenAPI specification content
  openApiSpec Json // Stores the complete OpenAPI specification
  version     String? // OpenAPI version (3.0.2, 3.1.0, etc.)

  // File storage information
  originalFile String? // Reference to uploaded spec file (JSON/YAML)
  fileSize     Int? // Size in bytes
  storageType  DocumentStorageType @default(VERCEL_BLOB)

  // Status and metadata
  status    ApiSpecStatus @default(PROCESSING)
  enabled   <PERSON><PERSON><PERSON>       @default(true)
  toolCount Int           @default(0) // Number of generated tools

  // Ownership and team management (leveraging existing Papermark structure)
  owner   User?   @relation(fields: [ownerId], references: [id], onDelete: SetNull)
  ownerId String?
  team    Team    @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId  String

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tools  FunctionTool[] // Generated function tools
  agents Agent[] // AI agents created from this spec
  tags   TagItem[] // Tagging support

  @@index([ownerId])
  @@index([teamId])
  @@index([status])
}

enum ApiSpecStatus {
  PROCESSING // Being parsed and validated
  ACTIVE // Successfully processed and ready
  ERROR // Failed to process
  DISABLED // Manually disabled
}

model FunctionTool {
  id          String @id @default(cuid())
  name        String // Function name (e.g., "get_pet_by_id")
  description String // Human-readable description

  // Tool configuration
  enabled      Boolean @default(true)
  userModified Boolean @default(false) // Whether user has customized this tool

  // API endpoint information
  endpoint Json // Stores complete endpoint info: {path, method, operationId, etc.}

  // Function parameters schema
  parameters Json // OpenAI function calling parameter schema

  // Response handling
  responseSchema Json? // Expected response schema from OpenAPI

  // Relationships
  apiSpec   ApiSpecification @relation(fields: [apiSpecId], references: [id], onDelete: Cascade)
  apiSpecId String

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations - tools can be used by multiple agents
  agentTools AgentTool[]

  @@unique([name, apiSpecId]) // Unique tool names per API spec
  @@index([apiSpecId])
  @@index([enabled])
}

model Agent {
  id          String  @id @default(cuid())
  name        String
  description String?

  // Agent configuration
  systemPrompt String? // Custom system prompt
  status       AgentStatus @default(ACTIVE)

  // AI model configuration
  modelConfig Json? // {model: "gpt-4.1-nano", temperature: 0.7, maxTokens: 1000}

  // Usage statistics
  conversationCount Int       @default(0)
  messageCount      Int       @default(0)
  lastUsed          DateTime?

  // Ownership and team management
  owner   User?   @relation(fields: [ownerId], references: [id], onDelete: SetNull)
  ownerId String?
  team    Team    @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId  String

  // Associated API specification
  apiSpec   ApiSpecification @relation(fields: [apiSpecId], references: [id], onDelete: Cascade)
  apiSpecId String

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tools         AgentTool[] // Selected function tools
  conversations AgentConversation[] // Chat conversations

  @@index([ownerId])
  @@index([teamId])
  @@index([apiSpecId])
  @@index([status])
}

enum AgentStatus {
  ACTIVE // Ready for conversations
  PAUSED // Temporarily disabled
  ERROR // Configuration error
  TRAINING // Being configured/updated
}

// Junction table for Agent <-> FunctionTool many-to-many relationship
model AgentTool {
  id String @id @default(cuid())

  agent   Agent        @relation(fields: [agentId], references: [id], onDelete: Cascade)
  agentId String
  tool    FunctionTool @relation(fields: [toolId], references: [id], onDelete: Cascade)
  toolId  String

  // Tool-specific configuration for this agent
  enabled      Boolean @default(true)
  customConfig Json? // Agent-specific tool configuration

  createdAt DateTime @default(now())

  @@unique([agentId, toolId])
  @@index([agentId])
  @@index([toolId])
}

model AgentConversation {
  id        String  @id @default(cuid())
  sessionId String  @unique // External session identifier
  title     String? // Conversation title/summary

  // Conversation metadata
  status ConversationStatus @default(ACTIVE)

  // Usage statistics
  messageCount Int @default(0)
  apiCallCount Int @default(0)
  totalTokens  Int @default(0)

  // Ownership
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId String?

  // Associated agent
  agent   Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
  agentId String

  // Timestamps
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastMessageAt DateTime?

  // Relations
  messages AgentMessage[]

  @@index([userId])
  @@index([agentId])
  @@index([sessionId])
  @@index([status])
}

enum ConversationStatus {
  ACTIVE // Ongoing conversation
  COMPLETED // User ended conversation
  ERROR // Error occurred
  ARCHIVED // Archived for cleanup
}

model AgentMessage {
  id      String      @id @default(cuid())
  content String      @db.Text
  role    MessageRole // user, assistant, system

  // Message metadata
  tokenCount Int? // Number of tokens in this message

  // API execution information (for assistant messages)
  apiExecutions Json? // Array of API calls made: [{tool, success, responseTime, etc.}]

  // Relationships
  conversation   AgentConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  conversationId String

  // Timestamps
  createdAt DateTime @default(now())

  @@index([conversationId])
  @@index([role])
  @@index([createdAt])
}

enum MessageRole {
  USER // User input
  ASSISTANT // AI agent response
  SYSTEM // System messages
}

// Extend existing models to support API2Agent functionality
// Add relations to Team model for API specifications and agents
// These additions should be added to the existing Team model:
// apiSpecs  ApiSpecification[]
// agents    Agent[]

// Add relations to User model for conversations
// These additions should be added to the existing User model:
// ownedApiSpecs    ApiSpecification[]
// ownedAgents      Agent[]
// agentConversations AgentConversation[]
