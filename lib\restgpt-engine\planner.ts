/**
 * Planner Component - Decomposes complex user instructions into sub-tasks
 * Inspired by RestGPT's Planner that creates natural language plans
 */
import { Agent, run, tool } from "@openai/agents";

import {
  ConversationContext,
  EngineConfig,
  FunctionTool,
  NaturalLanguagePlan,
} from "./types";

export class TaskPlanner {
  private agent: Agent;

  constructor(private config: EngineConfig) {
    // Agent will be initialized in the initialize method
    this.agent = new Agent({
      name: "task-planner-agent",
      model: "gpt-4o",
      instructions: "", // Will be set in initialize
    });
  }

  /**
   * Initialize the planning agent
   */
  async initialize(availableTools: FunctionTool[]): Promise<void> {
    const systemPrompt = this.buildPlannerSystemPrompt(availableTools);
    try {
      // Update the agent with the system prompt and tools
      this.agent = new Agent({
        name: "task-planner-agent",
        model: "gpt-4o",
        instructions: systemPrompt,
        tools: this.convertToOpenAITools(availableTools),
      });
    } catch (error) {
      console.error("Failed to create planner agent:", error);
      throw error;
    }
  }

  /**
   * Create a natural language plan from user request
   */
  async createPlan(
    userRequest: string,
    context: ConversationContext,
  ): Promise<NaturalLanguagePlan> {
    if (!this.agent) {
      throw new Error("Planner not initialized. Call initialize() first.");
    }
    const planningPrompt = this.buildPlanningPrompt(userRequest, context);
    try {
      const result = await run(this.agent, planningPrompt);
      if (result.finalOutput) {
        return this.parsePlanResponse(result.finalOutput);
      }
      throw new Error("Failed to generate plan");
    } catch (error) {
      console.error("Planning error:", error);
      throw new Error(
        `Planning failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Update plan based on execution results
   */
  async updatePlan(
    currentPlan: NaturalLanguagePlan,
    executionResult: any,
    context: ConversationContext,
  ): Promise<NaturalLanguagePlan> {
    if (!this.agent) {
      throw new Error("Planner not initialized");
    }
    const updatePrompt = this.buildUpdatePrompt(
      currentPlan,
      executionResult,
      context,
    );
    try {
      const result = await run(this.agent, updatePrompt);
      if (result.finalOutput) {
        return this.parsePlanResponse(result.finalOutput);
      }
      throw new Error("Failed to update plan");
    } catch (error) {
      console.error("Plan update error:", error);
      throw new Error(
        `Plan update failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Build system prompt for the planner agent
   */
  private buildPlannerSystemPrompt(availableTools: FunctionTool[]): string {
    const toolDescriptions = availableTools
      .filter((tool) => tool.enabled)
      .map((tool) => `- ${tool.name}: ${tool.description}`)
      .join("\n");

    return `You are a task planning agent inspired by RestGPT. Your role is to decompose complex user requests into manageable sub-tasks.

Available API Tools:
${toolDescriptions}

Your responsibilities:
1. Analyze user requests and break them down into logical steps
2. Create natural language plans that specify what needs to be done
3. Determine the next action: 'continue', 'next', or 'end'
4. Maintain context between planning iterations
5. Assess execution results and adapt plans accordingly

Guidelines:
- Keep plans clear and actionable
- Consider API dependencies and data flow
- Use available tools efficiently
- Handle errors gracefully
- Provide meaningful progress updates

Response Format:
Always respond with a JSON object containing:
{
  "currentStep": number,
  "totalSteps": number,
  "currentTask": "description of current task",
  "nextAction": "continue|next|end",
  "context": {"key": "value pairs for maintaining state"}
}`;
  }

  /**
   * Build planning prompt for user request
   */
  private buildPlanningPrompt(
    userRequest: string,
    context: ConversationContext,
  ): string {
    const historyContext = context.messageHistory
      .slice(-5) // Last 5 messages for context
      .map((msg) => `${msg.role}: ${msg.content}`)
      .join("\n");

    const executionContext = context.executionHistory
      .slice(-3) // Last 3 executions for context
      .map(
        (exec) =>
          `Executed: ${exec.plan.toolId} -> ${exec.result.success ? "Success" : "Failed"}`,
      )
      .join("\n");

    return `Create a plan for the following user request:

USER REQUEST: ${userRequest}

CONVERSATION CONTEXT:
${historyContext}

RECENT EXECUTIONS:
${executionContext}

Please create a detailed plan that breaks down this request into manageable steps using the available API tools.`;
  }

  /**
   * Build update prompt for plan modification
   */
  private buildUpdatePrompt(
    currentPlan: NaturalLanguagePlan,
    executionResult: any,
    context: ConversationContext,
  ): string {
    return `Update the current plan based on the execution result:

CURRENT PLAN:
Step ${currentPlan.currentStep}/${currentPlan.totalSteps}: ${currentPlan.currentTask}
Next Action: ${currentPlan.nextAction}

EXECUTION RESULT:
Success: ${executionResult.success}
Data: ${JSON.stringify(executionResult.data || {}, null, 2)}
Error: ${executionResult.error || "None"}

CONTEXT:
${JSON.stringify(currentPlan.context, null, 2)}

Please update the plan based on this result. Determine if we should continue the current task, move to the next task, or end the execution.`;
  }

  /**
   * Parse plan response from LLM
   */
  private parsePlanResponse(response: string): NaturalLanguagePlan {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        currentStep: parsed.currentStep || 1,
        totalSteps: parsed.totalSteps || 1,
        currentTask: parsed.currentTask || "Process request",
        nextAction: parsed.nextAction || "end",
        context: parsed.context || {},
      };
    } catch (error) {
      console.error("Failed to parse plan response:", error);
      // Fallback plan
      return {
        currentStep: 1,
        totalSteps: 1,
        currentTask: "Process the user request",
        nextAction: "end",
        context: {},
      };
    }
  }

  /**
   * Convert function tools to OpenAI Agents SDK tools format
   */
  private convertToOpenAITools(tools: FunctionTool[]): any[] {
    return tools
      .filter((tool) => tool.enabled)
      .map((functionTool) =>
        tool({
          name: functionTool.name,
          description: functionTool.description,
          parameters: {
            type: "object" as const,
            properties: functionTool.parameters.properties,
            required: functionTool.parameters.required || [],
            additionalProperties: false,
          },
          execute: async () => {
            // This is a placeholder - the actual execution will be handled elsewhere
            return "Tool execution placeholder";
          },
        }),
      );
  }
}
