module.exports = {
  semi: true,
  singleQuote: false,
  jsxSingleQuote: false,
  bracketSpacing: true,
  singleAttributePerLine: false,
  bracketSameLine: false,
  tabWidth: 2,
  useTabs: false,
  trailingComma: "all",
  printWidth: 80,
  quoteProps: "as-needed",
  arrowParens: "always",
  endOfLine: "lf",
  proseWrap: "preserve",
  importOrder: [
    "^(next/(.*)$)|^(next$)",
    "^(react/(.*)$)|^(react$)",
    "<THIRD_PARTY_MODULES>",
    "^@/lib/(.*)$",
    "^@/components/(.*)$|^components/(.*)$",
    "^[./]",
    "^@/styles/(.*)$",
  ],
  importOrderSeparation: true,
  importOrderSortSpecifiers: true,
  plugins: [
    "@trivago/prettier-plugin-sort-imports",
    "prettier-plugin-tailwindcss",
  ],
};
