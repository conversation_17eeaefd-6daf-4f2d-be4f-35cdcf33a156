/**
 * RestGPT Engine - API to Agent Conversion System
 *
 * A TypeScript implementation inspired by RestGPT's Parse -> Plan -> Execute strategy
 * Built for Next.js with OpenAI Agents SDK integration
 */
import { RestGPTEngine } from "./engine";

export { RestGPTEngine } from "./engine";
export { APISpecParser } from "./parser";
export { TaskPlanner } from "./planner";
export { APIExecutor } from "./executor";
export * from "./types";

// Convenience factory function
export function createRestGPTEngine(config: {
  maxRetries?: number;
  timeout?: number;
  enableLogging?: boolean;
  baseUrl?: string;
}) {
  return new RestGPTEngine({
    maxRetries: config.maxRetries || 3,
    timeout: config.timeout || 30000,
    enableLogging: config.enableLogging || false,
    baseUrl: config.baseUrl,
  });
}
