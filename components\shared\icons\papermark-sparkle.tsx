export default function PapermarkSparkle({
  className,
}: {
  className?: string;
}) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 40 40"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      shapeRendering="geometricPrecision"
      className={className}
    >
      <path
        d="M31 12L29.7253 18.1359C29.6601 18.45 29.5492 18.7354 29.4016 18.9691C29.2539 19.2029 29.0737 19.3785 28.8753 19.4818L25 21.5L28.8753 23.5182C29.0737 23.6215 29.2539 23.7971 29.4016 24.0309C29.5492 24.2646 29.6601 24.55 29.7253 24.8641L31 31L32.2747 24.8641C32.3399 24.55 32.4508 24.2646 32.5984 24.0309C32.7461 23.7971 32.9263 23.6215 33.1247 23.5182L37 21.5L33.1247 19.4818C32.9263 19.3785 32.7461 19.2029 32.5984 18.9691C32.4508 18.7354 32.3399 18.45 32.2747 18.1359L31 12Z"
        fill="currentColor"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.89631 31V9.18182H16.5043C18.1591 9.18182 19.5689 9.49787 20.7337 10.13C21.8984 10.755 22.7862 11.625 23.397 12.7401C24.0149 13.848 24.3239 15.1264 24.3239 16.5753C24.3239 18.0241 24.0114 19.3026 23.3864 20.4105C22.7614 21.5185 21.8558 22.3814 20.6697 22.9993C19.4908 23.6172 18.0632 23.9261 16.3871 23.9261H10.9006V20.2294H15.6413C16.5291 20.2294 17.2607 20.0767 17.8359 19.7713C18.4183 19.4588 18.8516 19.0291 19.1357 18.4822C19.4268 17.9283 19.5724 17.2926 19.5724 16.5753C19.5724 15.8509 19.4268 15.2187 19.1357 14.679C18.8516 14.1321 18.4183 13.7095 17.8359 13.4112C17.2536 13.1058 16.5149 12.9531 15.62 12.9531H12.5092V31H7.89631Z"
        fill="currentColor"
      />
      <path
        d="M26.5 5L25.9689 6.93767C25.9417 7.03684 25.8955 7.12696 25.834 7.20078C25.7725 7.2746 25.6974 7.33005 25.6147 7.36267L24 8L25.6147 8.63733C25.6974 8.66995 25.7725 8.7254 25.834 8.79922C25.8955 8.87304 25.9417 8.96316 25.9689 9.06233L26.5 11L27.0311 9.06233C27.0583 8.96316 27.1045 8.87304 27.166 8.79922C27.2275 8.7254 27.3026 8.66995 27.3853 8.63733L29 8L27.3853 7.36267C27.3026 7.33005 27.2275 7.2746 27.166 7.20078C27.1045 7.12696 27.0583 7.03684 27.0311 6.93767L26.5 5Z"
        fill="currentColor"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.5 27L21.9689 28.9377C21.9417 29.0368 21.8955 29.127 21.834 29.2008C21.7725 29.2746 21.6974 29.33 21.6147 29.3627L20 30L21.6147 30.6373C21.6974 30.67 21.7725 30.7254 21.834 30.7992C21.8955 30.873 21.9417 30.9632 21.9689 31.0623L22.5 33L23.0311 31.0623C23.0583 30.9632 23.1045 30.873 23.166 30.7992C23.2275 30.7254 23.3026 30.67 23.3853 30.6373L25 30L23.3853 29.3627C23.3026 29.33 23.2275 29.2746 23.166 29.2008C23.1045 29.127 23.0583 29.0368 23.0311 28.9377L22.5 27Z"
        fill="currentColor"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
