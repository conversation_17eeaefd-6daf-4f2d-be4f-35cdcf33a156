/**
 * API Route: Get, update, or delete a specific API specification
 * Replaces Papermark's individual document management
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import prisma from "@/lib/prisma";
import { CustomUser } from "@/lib/types";

import { authOptions } from "../auth/[...nextauth]";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const { id } = req.query;
    if (!id || typeof id !== "string") {
      return res.status(400).json({ error: "Invalid specification ID" });
    }

    switch (req.method) {
      case "GET":
        return handleGet(req, res, id, session);
      case "PUT":
        return handleUpdate(req, res, id, session);
      case "DELETE":
        return handleDelete(req, res, id, session);
      default:
        return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error) {
    console.error("API Spec operation error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

async function handleGet(
  req: NextApiRequest,
  res: NextApiResponse,
  specId: string,
  session: any,
) {
  // Get API specification from database
  const apiSpec = await prisma.apiSpecification.findFirst({
    where: {
      id: specId,
      OR: [
        { ownerId: (session.user as CustomUser).id },
        {
          team: {
            users: { some: { userId: (session.user as CustomUser).id } },
          },
        },
      ],
    },
    include: {
      tools: {
        orderBy: { name: "asc" },
      },
      owner: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      team: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  if (!apiSpec) {
    return res.status(404).json({
      error: "API specification not found",
    });
  }

  // Format tools for response
  const formattedTools = apiSpec.tools.map((tool) => ({
    id: tool.id,
    name: tool.name,
    description: tool.description,
    enabled: tool.enabled,
    userModified: tool.userModified,
    endpoint: tool.endpoint,
    parameters: tool.parameters,
    responseSchema: tool.responseSchema,
    createdAt: tool.createdAt,
    updatedAt: tool.updatedAt,
  }));

  res.status(200).json({
    success: true,
    data: {
      id: apiSpec.id,
      name: apiSpec.name,
      description: apiSpec.description,
      baseUrl: apiSpec.baseUrl,
      openApiSpec: apiSpec.openApiSpec,
      version: apiSpec.version,
      status: apiSpec.status,
      enabled: apiSpec.enabled,
      toolCount: apiSpec.toolCount,
      tools: formattedTools,
      owner: apiSpec.owner,
      team: apiSpec.team,
      createdAt: apiSpec.createdAt,
      updatedAt: apiSpec.updatedAt,
    },
  });
}

async function handleUpdate(
  req: NextApiRequest,
  res: NextApiResponse,
  specId: string,
  session: any,
) {
  const { name, description, baseUrl, enabled } = req.body;

  // Check if user has permission to update this spec
  const existingSpec = await prisma.apiSpecification.findFirst({
    where: {
      id: specId,
      OR: [
        { ownerId: (session.user as CustomUser).id },
        {
          team: {
            users: { some: { userId: (session.user as CustomUser).id } },
          },
        },
      ],
    },
  });

  if (!existingSpec) {
    return res.status(404).json({
      error: "API specification not found",
    });
  }

  // Update the specification
  const updatedSpec = await prisma.apiSpecification.update({
    where: { id: specId },
    data: {
      ...(name && { name }),
      ...(description !== undefined && { description }),
      ...(baseUrl && { baseUrl }),
      ...(enabled !== undefined && { enabled }),
    },
  });

  res.status(200).json({
    success: true,
    message: "API specification updated successfully",
    data: {
      id: updatedSpec.id,
      name: updatedSpec.name,
      description: updatedSpec.description,
      baseUrl: updatedSpec.baseUrl,
      enabled: updatedSpec.enabled,
      updatedAt: updatedSpec.updatedAt,
    },
  });
}

async function handleDelete(
  req: NextApiRequest,
  res: NextApiResponse,
  specId: string,
  session: any,
) {
  // Check if user has permission to delete this spec
  const existingSpec = await prisma.apiSpecification.findFirst({
    where: {
      id: specId,
      OR: [
        { ownerId: (session.user as CustomUser).id },
        {
          team: {
            users: { some: { userId: (session.user as CustomUser).id } },
          },
        },
      ],
    },
  });

  if (!existingSpec) {
    return res.status(404).json({
      error: "API specification not found",
    });
  }

  // Delete the specification (cascade will handle tools, agents, etc.)
  await prisma.apiSpecification.delete({
    where: { id: specId },
  });

  res.status(200).json({
    success: true,
    message: "API specification deleted successfully",
  });
}
