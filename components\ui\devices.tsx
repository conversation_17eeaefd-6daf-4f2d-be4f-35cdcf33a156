// Thanks to <PERSON> for the original code: https://github.com/dubinc/dub/blob/652f17677828c5a9d5d354841b0bfba5fe63c7a8/apps/web/ui/shared/icons/devices.tsx

export function Chrome({ className }: { className: string }) {
  return (
    <svg viewBox="0 0 100 100" className={className}>
      <linearGradient
        id="b"
        x1="55.41"
        x2="12.11"
        y1="96.87"
        y2="21.87"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor="#1e8e3e" />
        <stop offset="1" stopColor="#34a853" />
      </linearGradient>
      <linearGradient
        id="c"
        x1="42.7"
        x2="86"
        y1="100"
        y2="25.13"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor="#fcc934" />
        <stop offset="1" stopColor="#fbbc04" />
      </linearGradient>
      <linearGradient
        id="a"
        x1="6.7"
        x2="93.29"
        y1="31.25"
        y2="31.25"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor="#d93025" />
        <stop offset="1" stopColor="#ea4335" />
      </linearGradient>
      <path fill="url(#a)" d="M93.29 25a50 50 90 0 0-86.6 0l3 54z" />
      <path fill="url(#b)" d="M28.35 62.5 6.7 25A50 50 90 0 0 50 100l49-50z" />
      <path fill="url(#c)" d="M71.65 62.5 50 100a50 50 90 0 0 43.29-75H50z" />
      <path fill="#fff" d="M50 75a25 25 90 1 0 0-50 25 25 90 0 0 0 50z" />
      <path
        fill="#1a73e8"
        d="M50 69.8a19.8 19.8 90 1 0 0-39.6 19.8 19.8 90 0 0 0 39.6z"
      />{" "}
    </svg>
  );
}

export function Safari({ className }: { className: string }) {
  return (
    <svg className={className} width="66" height="66" viewBox="0 0 66 66">
      <path
        fill="#C6C6C6"
        stroke="#C6C6C6"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="0.5"
        d="M383.29373 211.97671a31.325188 31.325188 0 0 1-31.32519 31.32519 31.325188 31.325188 0 0 1-31.32518-31.32519 31.325188 31.325188 0 0 1 31.32518-31.32519 31.325188 31.325188 0 0 1 31.32519 31.32519z"
        paintOrder="markers stroke fill"
        transform="translate(-318.88562 -180.59501)"
      />
      <path
        fill="#4A9DED"
        d="M380.83911 211.97671a28.870571 28.870571 0 0 1-28.87057 28.87057 28.870571 28.870571 0 0 1-28.87057-28.87057 28.870571 28.870571 0 0 1 28.87057-28.87057 28.870571 28.870571 0 0 1 28.87057 28.87057z"
        paintOrder="markers stroke fill"
        transform="translate(-318.88562 -180.59501)"
      />
      <path
        fill="#ff5150"
        d="m36.3834003 34.83806178-6.60095092-6.91272438 23.41607429-15.75199774z"
        paintOrder="markers stroke fill"
      />
      <path
        fill="#f1f1f1"
        d="m36.38339038 34.83805895-6.60095092-6.91272438-16.81512624 22.66471911z"
        paintOrder="markers stroke fill"
      />
      <path
        d="m12.96732 50.59006 23.41607-15.75201 16.81513-22.66472z"
        opacity=".243"
      />
    </svg>
  );
}

export function Apple({ className }: { className: string }) {
  return (
    <svg
      viewBox="0 0 2048 2048"
      width="2048px"
      height="2048px"
      className={className}
    >
      <path
        fill="#424242"
        fillRule="nonzero"
        d="M1318.64 413.756c-14.426,44.2737 -37.767,85.3075 -65.8997,119.436l0 0.0625985c-28.3855,34.324 -66.3012,64.6713 -108.482,84.7926 -38.713,18.4665 -81.1489,28.4114 -123.377,25.1197l-12.9236 -1.00748 -1.70197 -12.8681c-5.48622,-41.4992 0.849213,-83.5099 14.1921,-122.387 15.5268,-45.241 40.6772,-86.5205 67.6642,-117.8l-0.00472441 -0.00472441c27.9272,-32.7142 65.3788,-61.1776 105.487,-81.8009 40.2437,-20.6941 83.465,-33.6343 122.803,-35.237l14.8701 -0.605906 1.62992 14.8559c4.76457,43.4481 -1.02992,86.8489 -14.2571,127.445z"
      />
      <path
        fill="#424242"
        fillRule="nonzero"
        d="M1592.05 804.067c-14.2559,8.82048 -152.045,94.0808 -150.337,265.937 1.80236,207.182 177.474,279.003 187.171,282.966l0.0625985 0 0.419292 0.173622 13.7835 5.70709 -4.72087 14.1047c-0.279921,0.836221 0.0377953,-0.0531496 -0.370866,1.25906 -4.48229,14.361 -34.8685,111.708 -103.511,212.014 -31.1481,45.4985 -62.8831,90.9284 -100.352,125.971 -38.7957,36.2823 -83.1024,60.7737 -137.837,61.7906 -51.5894,0.968505 -85.3642,-13.6453 -120.474,-28.8366 -33.4784,-14.4862 -68.2949,-29.5524 -122.779,-29.5524 -57.2339,0 -93.8198,15.5858 -129.06,30.6 -33.1725,14.1319 -65.2548,27.7996 -111.474,29.6433l-0.0625985 0c-53.3693,1.98189 -99.6485,-24.0343 -140.778,-62.5678 -39.3496,-36.8646 -73.8249,-85.1398 -105.241,-130.579 -70.917,-102.399 -132.592,-251.392 -151.647,-402.892 -15.6732,-124.616 -2.57244,-251.206 57.6756,-355.753 33.6331,-58.4953 80.6398,-106.233 135.598,-139.543 54.7075,-33.1571 117.299,-52.0264 182.451,-53.0032l0 -0.0011811c57.0402,-1.03465 110.823,20.3091 157.884,38.9847 33.3059,13.2165 62.98,24.9933 85.226,24.9933 19.6536,0 48.6237,-11.4224 82.3949,-24.737 57.0367,-22.487 126.815,-49.9949 200.599,-42.6579 30.9862,1.34764 95.5265,8.76969 161.501,44.524 42.0284,22.7776 84.6579,56.9741 119.701,108.261l9.23977 13.5248 -13.8024 8.91261c-0.73819,0.477166 -0.0200788,-0.00944883 -1.25906,0.755906z"
      />
    </svg>
  );
}
