import OpenAI from "openai";

// Prefer Gemini if GEMINI_API_KEY is set, otherwise use OpenAI
const geminiApiKey = process.env.GEMINI_API_KEY;
const openaiApiKey = process.env.OPENAI_API_KEY;

export const openai = new OpenAI({
  dangerouslyAllowBrowser: true,
  apiKey: openaiApiKey || geminiApiKey || "",
  ...(openaiApiKey
    ? {}
    : { baseURL: "https://generativelanguage.googleapis.com/v1beta/openai/" }),
});
