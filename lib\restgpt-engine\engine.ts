/**
 * Main RestGPT Engine - Orchestrates <PERSON><PERSON>r, Planner, and Executor
 * Implements the full Parse -> Plan -> Execute workflow
 */

import { APISpecParser } from './parser';
import { TaskPlanner } from './planner';
import { APIExecutor } from './executor';
import {
  OpenAPISpec,
  FunctionTool,
  ConversationContext,
  NaturalLanguagePlan,
  APICallPlan,
  ExecutionResult,
  EngineConfig
} from './types';

export class RestGPTEngine {
  private parser: APISpecParser;
  private planner: TaskPlanner;
  private executor: APIExecutor;
  private tools: FunctionTool[] = [];
  private baseUrl: string = '';

  constructor(private config: EngineConfig) {
    this.parser = new APISpecParser();
    this.planner = new TaskPlanner(config);
    this.executor = new APIExecutor(config);
  }

  /**
   * Initialize the engine with an OpenAPI specification
   */
  async initialize(spec: OpenAPISpec, baseUrl: string): Promise<{
    success: boolean;
    tools: FunctionTool[];
    errors?: string[];
  }> {
    try {
      // Validate the OpenAPI spec
      const validation = this.parser.validateOpenAPISpec(spec);
      if (!validation.valid) {
        return {
          success: false,
          tools: [],
          errors: validation.errors
        };
      }

      this.baseUrl = baseUrl;

      // Parse the spec and generate tools
      const endpoints = this.parser.parseOpenAPISpec(spec);
      this.tools = this.parser.generateFunctionTools(endpoints, baseUrl);

      // Initialize agents
      await this.planner.initialize(this.tools);
      await this.executor.initialize(this.tools);

      if (this.config.enableLogging) {
        console.log(`RestGPT Engine initialized with ${this.tools.length} tools`);
      }

      return {
        success: true,
        tools: this.tools
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
      return {
        success: false,
        tools: [],
        errors: [errorMessage]
      };
    }
  }

  /**
   * Process a user request using the Parse -> Plan -> Execute workflow
   */
  async processRequest(
    userRequest: string,
    context: ConversationContext
  ): Promise<{
    success: boolean;
    response: string;
    executionHistory: Array<{
      plan: APICallPlan;
      result: ExecutionResult;
    }>;
    error?: string;
  }> {
    try {
      const executionHistory: Array<{ plan: APICallPlan; result: ExecutionResult }> = [];
      
      // Step 1: Create initial plan
      let currentPlan = await this.planner.createPlan(userRequest, context);
      
      // Step 2: Execute plan iteratively
      while (currentPlan.nextAction !== 'end') {
        // Convert natural language plan to API call plan
        const apiPlan = await this.createAPICallPlan(currentPlan, context);
        
        if (apiPlan) {
          // Execute the API call
          const result = await this.executor.executeAPICall(
            apiPlan,
            this.baseUrl,
            currentPlan.context
          );

          executionHistory.push({ plan: apiPlan, result });

          // Update context with execution result
          context.executionHistory.push({
            plan: apiPlan,
            result,
            timestamp: new Date()
          });

          // Update plan based on result
          currentPlan = await this.planner.updatePlan(currentPlan, result, context);
        } else {
          // No valid API plan could be created, end execution
          break;
        }

        // Safety check to prevent infinite loops
        if (executionHistory.length > 10) {
          break;
        }
      }

      // Generate final response
      const response = await this.generateFinalResponse(
        userRequest,
        executionHistory,
        currentPlan
      );

      return {
        success: true,
        response,
        executionHistory
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown processing error';
      
      if (this.config.enableLogging) {
        console.error('RestGPT Engine processing error:', error);
      }

      return {
        success: false,
        response: 'I encountered an error while processing your request. Please try again.',
        executionHistory: [],
        error: errorMessage
      };
    }
  }

  /**
   * Get available tools (for frontend display)
   */
  getAvailableTools(): FunctionTool[] {
    return this.tools.filter(tool => tool.enabled);
  }

  /**
   * Update tool configuration
   */
  updateTool(toolId: string, updates: Partial<FunctionTool>): boolean {
    const toolIndex = this.tools.findIndex(tool => tool.id === toolId);
    if (toolIndex === -1) return false;

    this.tools[toolIndex] = {
      ...this.tools[toolIndex],
      ...updates,
      userModified: true
    };

    return true;
  }

  /**
   * Enable/disable a tool
   */
  toggleTool(toolId: string, enabled: boolean): boolean {
    return this.updateTool(toolId, { enabled });
  }

  /**
   * Create API call plan from natural language plan
   */
  private async createAPICallPlan(
    nlPlan: NaturalLanguagePlan,
    context: ConversationContext
  ): Promise<APICallPlan | null> {
    // This is a simplified version - in a full implementation,
    // this would use an API Selector agent to choose the right tool
    
    const availableTools = this.tools.filter(tool => tool.enabled);
    if (availableTools.length === 0) return null;

    // For now, use the first available tool
    // In practice, this would be more sophisticated tool selection
    const selectedTool = availableTools[0];
    
    return {
      toolId: selectedTool.id,
      endpoint: selectedTool.endpoint,
      parameters: nlPlan.context,
      expectedResponse: nlPlan.currentTask,
      outputInstruction: this.parser.generateParsingInstructions(
        selectedTool.endpoint,
        nlPlan.currentTask
      )
    };
  }

  /**
   * Generate final response based on execution history
   */
  private async generateFinalResponse(
    userRequest: string,
    executionHistory: Array<{ plan: APICallPlan; result: ExecutionResult }>,
    finalPlan: NaturalLanguagePlan
  ): Promise<string> {
    if (executionHistory.length === 0) {
      return 'I was unable to find any suitable actions to complete your request.';
    }

    const successfulExecutions = executionHistory.filter(exec => exec.result.success);
    const failedExecutions = executionHistory.filter(exec => !exec.result.success);

    let response = `I've processed your request: "${userRequest}"\n\n`;

    if (successfulExecutions.length > 0) {
      response += 'Successfully executed:\n';
      successfulExecutions.forEach((exec, index) => {
        response += `${index + 1}. ${exec.plan.expectedResponse}\n`;
        if (exec.result.parsedOutput) {
          response += `   Result: ${JSON.stringify(exec.result.parsedOutput, null, 2)}\n`;
        }
      });
    }

    if (failedExecutions.length > 0) {
      response += '\nSome operations failed:\n';
      failedExecutions.forEach((exec, index) => {
        response += `${index + 1}. ${exec.plan.expectedResponse}: ${exec.result.error}\n`;
      });
    }

    return response;
  }

  /**
   * Health check for the engine
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    components: {
      parser: boolean;
      planner: boolean;
      executor: boolean;
    };
    toolCount: number;
  }> {
    const components = {
      parser: this.parser !== null,
      planner: this.planner !== null,
      executor: this.executor !== null
    };

    const healthyComponents = Object.values(components).filter(Boolean).length;
    const status = healthyComponents === 3 ? 'healthy' : 
                  healthyComponents >= 2 ? 'degraded' : 'unhealthy';

    return {
      status,
      components,
      toolCount: this.tools.length
    };
  }
}

// Export all components for external use
export { APISpecParser } from './parser';
export { TaskPlanner } from './planner';
export { APIExecutor } from './executor';
export * from './types';
