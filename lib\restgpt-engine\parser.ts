/**
 * Parser Component - Analyzes OpenAPI specifications and generates function tools
 * Inspired by RestGPT's Parser that generates code based on response schemas
 */
import { APIEndpoint, FunctionTool, OpenAPISpec } from "./types";

export class APISpecParser {
  private spec: OpenAPISpec | null = null;

  /**
   * Parse OpenAPI specification and extract all endpoints
   */
  parseOpenAPISpec(spec: OpenAPISpec): APIEndpoint[] {
    // Store the spec for reference resolution
    this.spec = spec;
    const endpoints: APIEndpoint[] = [];

    for (const [path, pathItem] of Object.entries(spec.paths)) {
      for (const [method, operation] of Object.entries(pathItem)) {
        if (typeof operation === "object" && operation !== null) {
          const op = operation as any; // Type assertion for OpenAPI operation object
          const endpoint: APIEndpoint = {
            path,
            method: method.toUpperCase(),
            operationId: op.operationId,
            summary: op.summary,
            description: op.description,
            parameters: op.parameters || [],
            requestBody: op.requestBody,
            responses: op.responses || {},
          };
          endpoints.push(endpoint);
        }
      }
    }

    return endpoints;
  }

  /**
   * Generate function tools from API endpoints
   */
  generateFunctionTools(
    endpoints: APIEndpoint[],
    baseUrl?: string,
  ): FunctionTool[] {
    return endpoints.map((endpoint, index) => {
      const toolName = this.generateToolName(endpoint);
      const description = this.generateToolDescription(endpoint);
      const parameters = this.extractParameters(endpoint);

      return {
        id: `tool_${index + 1}`,
        name: toolName,
        method: endpoint.method,
        description,
        endpoint,
        parameters,
        enabled: true,
        userModified: false,
      };
    });
  }

  /**
   * Generate a meaningful tool name from endpoint
   */
  private generateToolName(endpoint: APIEndpoint): string {
    if (endpoint.operationId) {
      return this.camelToSnakeCase(endpoint.operationId);
    }

    const pathParts = endpoint.path
      .split("/")
      .filter((part) => part && !part.startsWith("{"));
    const method = endpoint.method.toLowerCase();

    if (pathParts.length > 0) {
      const resource = pathParts[pathParts.length - 1];
      return `${method}_${resource}`;
    }

    return `${method}_endpoint`;
  }

  /**
   * Generate a descriptive tool description
   */
  private generateToolDescription(endpoint: APIEndpoint): string {
    if (endpoint.description) {
      return endpoint.description;
    }

    if (endpoint.summary) {
      return endpoint.summary;
    }

    const method = endpoint.method.toUpperCase();
    const path = endpoint.path;
    return `${method} ${path}`;
  }

  /**
   * Extract and convert parameters to function tool format
   */
  private extractParameters(endpoint: APIEndpoint): {
    type: "object";
    properties: Record<string, any>;
    required?: string[];
    additionalProperties: false;
  } {
    const properties: Record<string, any> = {};
    const required: string[] = [];

    // Process path parameters
    if (endpoint.parameters) {
      for (const param of endpoint.parameters) {
        const resolvedSchema = this.resolveReference(param.schema);
        properties[param.name] = {
          type: this.getSchemaType(resolvedSchema),
          description: param.description || `${param.in} parameter`,
          ...(resolvedSchema || {}),
        };

        if (param.required) {
          required.push(param.name);
        }
      }
    }

    // Process request body
    if (endpoint.requestBody) {
      const content = endpoint.requestBody.content;
      if (content["application/json"]) {
        const schema = content["application/json"].schema;
        if (schema) {
          const resolvedSchema = this.resolveReference(schema);
          if (resolvedSchema) {
            // Add the entire request body as a 'body' parameter
            properties.body = {
              ...resolvedSchema,
              additionalProperties: false,
            };
            if (endpoint.requestBody.required !== false) {
              required.push("body");
            }
          }
        }
      }
    }

    const result = {
      type: "object" as const,
      properties,
      ...(required.length > 0 && { required }),
      additionalProperties: false as const,
    };

    return result;
  }

  /**
   * Resolve $ref references in the API specification
   */
  private resolveReference(obj: any): any {
    if (!obj || typeof obj !== "object" || !this.spec) {
      return obj;
    }

    if (obj.$ref) {
      const refPath = obj.$ref.replace("#/", "").split("/");
      let resolved = this.spec as any;

      for (const part of refPath) {
        resolved = resolved[part];
        if (!resolved) {
          console.warn(`Could not resolve reference: ${obj.$ref}`);
          return obj;
        }
      }

      return this.resolveReference(resolved);
    }

    // Recursively resolve references in nested objects
    const result = Array.isArray(obj) ? [] : {};
    for (const [key, value] of Object.entries(obj)) {
      (result as any)[key] = this.resolveReference(value);
    }

    return this.ensureAdditionalPropertiesFalse(result);
  }

  /**
   * Recursively ensure all object schemas have additionalProperties: false
   */
  private ensureAdditionalPropertiesFalse(obj: any): any {
    if (!obj || typeof obj !== "object") {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.ensureAdditionalPropertiesFalse(item));
    }

    const result = { ...obj };

    // If this is an object schema, ensure it has additionalProperties: false
    if (result.type === "object" && result.properties) {
      result.additionalProperties = false;
    }

    // Recursively process all properties
    for (const [key, value] of Object.entries(result)) {
      if (typeof value === "object" && value !== null) {
        result[key] = this.ensureAdditionalPropertiesFalse(value);
      }
    }

    return result;
  }

  /**
   * Get OpenAPI schema type
   */
  private getSchemaType(schema: any): string {
    if (!schema) return "string";
    return schema.type || "string";
  }

  /**
   * Convert camelCase to snake_case
   */
  private camelToSnakeCase(str: string): string {
    return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
  }

  /**
   * Parse API response using schema information
   * This method generates parsing instructions for the Executor
   */
  generateParsingInstructions(
    endpoint: APIEndpoint,
    outputGoal: string,
  ): string {
    const responses = endpoint.responses;
    const successResponse =
      responses["200"] || responses["201"] || responses["default"];

    if (!successResponse || !successResponse.content) {
      return `Extract relevant information from the response that helps achieve: ${outputGoal}`;
    }

    const schema = successResponse.content["application/json"]?.schema;
    if (!schema) {
      return `Parse the JSON response and extract information relevant to: ${outputGoal}`;
    }

    let instructions = `Parse the JSON response according to the following schema and extract information for: ${outputGoal}\n`;

    if (schema.properties) {
      instructions += "Expected response structure:\n";
      for (const [key, value] of Object.entries(schema.properties)) {
        const prop = value as any;
        instructions += `- ${key}: ${prop.type || "unknown"} - ${prop.description || "No description"}\n`;
      }
    }

    return instructions;
  }

  /**
   * Validate OpenAPI specification
   */
  validateOpenAPISpec(spec: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!spec.openapi && !spec.swagger) {
      errors.push("Missing openapi or swagger version field");
    }

    if (!spec.info) {
      errors.push("Missing info object");
    } else {
      if (!spec.info.title) errors.push("Missing info.title");
      if (!spec.info.version) errors.push("Missing info.version");
    }

    if (!spec.paths || Object.keys(spec.paths).length === 0) {
      errors.push("Missing or empty paths object");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
