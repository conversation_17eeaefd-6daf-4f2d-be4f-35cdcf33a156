.spinner {
  position: relative;
  top: 50%;
  left: 50%;
}
.spinner div {
  animation: spinner 1.2s linear infinite;
  background: gray;
  position: absolute;
  border-radius: 1rem;
  width: 30%;
  height: 8%;
  left: -10%;
  top: -4%;
}
.spinner div:nth-child(1) {
  animation-delay: -1.2s;
  transform: rotate(1deg) translate(120%);
}
.spinner div:nth-child(2) {
  animation-delay: -1.1s;
  transform: rotate(30deg) translate(120%);
}
.spinner div:nth-child(3) {
  animation-delay: -1s;
  transform: rotate(60deg) translate(120%);
}
.spinner div:nth-child(4) {
  animation-delay: -0.9s;
  transform: rotate(90deg) translate(120%);
}
.spinner div:nth-child(5) {
  animation-delay: -0.8s;
  transform: rotate(120deg) translate(120%);
}
.spinner div:nth-child(6) {
  animation-delay: -0.7s;
  transform: rotate(150deg) translate(120%);
}
.spinner div:nth-child(7) {
  animation-delay: -0.6s;
  transform: rotate(180deg) translate(120%);
}
.spinner div:nth-child(8) {
  animation-delay: -0.5s;
  transform: rotate(210deg) translate(120%);
}
.spinner div:nth-child(9) {
  animation-delay: -0.4s;
  transform: rotate(240deg) translate(120%);
}
.spinner div:nth-child(10) {
  animation-delay: -0.3s;
  transform: rotate(270deg) translate(120%);
}
.spinner div:nth-child(11) {
  animation-delay: -0.2s;
  transform: rotate(300deg) translate(120%);
}
.spinner div:nth-child(12) {
  animation-delay: -0.1s;
  transform: rotate(330deg) translate(120%);
}

@keyframes spinner {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
