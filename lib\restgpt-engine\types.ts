/**
 * Type definitions for the RestGPT-inspired API-to-Agent engine
 * Based on RestGPT's Parse -> Plan -> Execute strategy
 */

export interface OpenAPISpec {
  openapi: string;
  info: {
    title: string;
    version: string;
    description?: string;
  };
  servers?: Array<{
    url: string;
    description?: string;
  }>;
  paths: Record<string, any>;
  components?: {
    schemas?: Record<string, any>;
    parameters?: Record<string, any>;
    responses?: Record<string, any>;
  };
}

export interface APIEndpoint {
  path: string;
  method: string;
  operationId?: string;
  summary?: string;
  description?: string;
  parameters?: Array<{
    name: string;
    in: "query" | "path" | "header" | "cookie";
    required?: boolean;
    schema: any;
    description?: string;
  }>;
  requestBody?: {
    required?: boolean;
    content: Record<string, any>;
  };
  responses: Record<string, any>;
}

export interface FunctionTool {
  id: string;
  name: string;
  description: string;
  endpoint: APIEndpoint;
  parameters: {
    type: "object";
    properties: Record<string, any>;
    required?: string[];
  };
  enabled: boolean;
  userModified?: boolean;
}

export interface NaturalLanguagePlan {
  currentStep: number;
  totalSteps: number;
  currentTask: string;
  nextAction: "continue" | "next" | "end";
  context: Record<string, any>;
}

export interface APICallPlan {
  toolId: string;
  endpoint: APIEndpoint;
  parameters: Record<string, any>;
  expectedResponse: string;
  outputInstruction: string;
}

export interface ExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  statusCode?: number;
  responseTime?: number;
  parsedOutput?: any;
}

export interface ConversationContext {
  sessionId: string;
  userId: string;
  agentId: string;
  messageHistory: Array<{
    role: "user" | "assistant" | "system";
    content: string;
    timestamp: Date;
  }>;
  executionHistory: Array<{
    plan: APICallPlan;
    result: ExecutionResult;
    timestamp: Date;
  }>;
}

export interface EngineConfig {
  maxRetries: number;
  timeout: number;
  enableLogging: boolean;
  baseUrl?: string;
}
